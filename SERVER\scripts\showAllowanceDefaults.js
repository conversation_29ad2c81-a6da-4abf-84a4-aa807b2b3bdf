/**
 * <PERSON><PERSON><PERSON> to demonstrate the new allowance default values
 */

const {
  calculateMedicalAllowance,
  calculateMealAllowance,
  calculateMonthlyMealAllowance,
  getAllowanceRates,
  formatAllowanceAmount,
  calculateEmployeeAllowances
} = require("../utils/allowanceUtils");

console.log("🏥💰 MEDICAL AND MEAL ALLOWANCE DEFAULT VALUES 💰🍽️");
console.log("=" .repeat(60));

console.log("\n📋 DEFAULT RATES:");
console.log("• Medical Allowance: ₱200 per month per dependent (max 4)");
console.log("• Meal Allowance: ₱200 per day (22 working days)");

console.log("\n🧮 CALCULATION EXAMPLES:");

// Medical allowance examples
console.log("\n🏥 Medical Allowance (Annual):");
for (let deps = 0; deps <= 5; deps++) {
  const amount = calculateMedicalAllowance(deps, 200);
  const effective = Math.min(deps, 4);
  console.log(`  ${deps} dependent${deps !== 1 ? 's' : ''} → ${formatAllowanceAmount(amount)} (effective: ${effective})`);
}

// Meal allowance examples
console.log("\n🍽️ Meal Allowance:");
const monthlyMeal = calculateMonthlyMealAllowance(22, 200);
const annualMeal = calculateMealAllowance(22, 200);
console.log(`  Monthly (22 days): ${formatAllowanceAmount(monthlyMeal)}`);
console.log(`  Annual (12 months): ${formatAllowanceAmount(annualMeal)}`);

console.log("\n👥 EMPLOYEE EXAMPLES:");

// Example employees
const employees = [
  {
    name: "Juan Dela Cruz",
    appointmentDate: new Date("1987-03-15"),
    dependents: 2,
    description: "Hired before June 1988, 2 dependents"
  },
  {
    name: "Maria Santos", 
    appointmentDate: new Date("1989-08-20"),
    dependents: 1,
    description: "Hired after June 1988, 1 dependent"
  },
  {
    name: "Pedro Reyes",
    appointmentDate: new Date("1985-12-10"),
    dependents: 4,
    description: "Hired before June 1988, 4 dependents"
  }
];

const settings = { medicalAllowance: 200, meal: 200 };

employees.forEach((emp, index) => {
  console.log(`\n${index + 1}. ${emp.name}`);
  console.log(`   ${emp.description}`);
  console.log(`   Appointment: ${emp.appointmentDate.toLocaleDateString()}`);
  
  const employee = {
    employeeFullName: emp.name,
    DateOfAppointment: emp.appointmentDate,
    noOfDependent: emp.dependents
  };
  
  const allowances = calculateEmployeeAllowances(employee, settings);
  
  console.log(`   Medical: ${allowances.medicalAllowance.eligible ? '✅' : '❌'} ${formatAllowanceAmount(allowances.medicalAllowance.annualAmount)}/year`);
  console.log(`   Meal: ${allowances.mealAllowance.eligible ? '✅' : '❌'} ${formatAllowanceAmount(allowances.mealAllowance.annualAmount)}/year`);
  
  if (allowances.medicalAllowance.eligible || allowances.mealAllowance.eligible) {
    const total = allowances.medicalAllowance.annualAmount + allowances.mealAllowance.annualAmount;
    console.log(`   Total: ${formatAllowanceAmount(total)}/year`);
  }
});

console.log("\n⚙️ SETTINGS CONFIGURATION:");
console.log("Default settings automatically include:");
console.log("```javascript");
console.log("{");
console.log('  "medicalAllowance": 200,  // ₱200 per month per dependent');
console.log('  "meal": 200               // ₱200 per day');
console.log("}");
console.log("```");

console.log("\n📝 CUSTOMIZATION:");
console.log("To change allowance amounts, update the settings:");
console.log("```javascript");
console.log("{");
console.log('  "medicalAllowance": 250,  // Increase to ₱250 per month per dependent');
console.log('  "meal": 300               // Increase to ₱300 per day');
console.log("}");
console.log("```");

console.log("\n🎯 KEY BENEFITS:");
console.log("✅ Fixed default values ensure consistency");
console.log("✅ Automatic calculations reduce errors");
console.log("✅ Configurable through settings");
console.log("✅ Backward compatible with existing data");
console.log("✅ Proper eligibility checking (hired before June 1988)");

console.log("\n" + "=" .repeat(60));
console.log("🎉 Medical and Meal Allowances are now set to ₱200 defaults!");
console.log("   Run the migration script to update existing settings.");
console.log("=" .repeat(60));

import React, { useState, useEffect, forwardRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  Box,
  Typography,
  Chip,
  Stack,
  useTheme,
  alpha,
  MenuItem,
  Alert,
  Divider,
  InputAdornment
} from "@mui/material";
import {
  <PERSON>d<PERSON><PERSON>,
  MdAttachMoney,
  MdSave,
  MdAdd
} from "react-icons/md";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { NumericFormat } from "react-number-format";
import api from "../../config/api";

// Validation schema
const rataSchema = yup.object().shape({
  SG: yup.string().required("Salary Grade is required"),
  RATA: yup.number()
    .required("RATA amount is required")
    .min(0, "RATA amount must be positive")
    .max(50000, "RATA amount seems too high, please verify")
});

// Predefined salary grades for dropdown
const salaryGrades = Array.from({ length: 33 }, (_, i) => (i + 1).toString());

// Custom Number Format Component for Peso Currency
const PesoFormatCustom = forwardRef(function PesoFormatCustom(props, ref) {
  const { onChange, ...other } = props;

  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      onValueChange={(values) => {
        onChange({
          target: {
            name: props.name,
            value: values.value,
          },
        });
      }}
      thousandSeparator={true}
      valueIsNumericString={true}
      prefix="₱"
      decimalScale={2}
      fixedDecimalScale={false}
      allowNegative={false}
    />
  );
});

const EnhancedRataDialog = ({ open, onClose, editData = null }) => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const isEditing = Boolean(editData);

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(rataSchema),
    defaultValues: {
      SG: editData?.SG || "",
      RATA: editData?.RATA || 0
    }
  });

  // Watch form values for real-time calculations
  const watchedValues = watch();

  // Reset form when editData changes
  useEffect(() => {
    if (editData) {
      reset({
        SG: editData.SG,
        RATA: editData.RATA
      });
    } else {
      reset({
        SG: "",
        RATA: 0
      });
    }
  }, [editData, reset]);

  // Mutation for saving RATA
  const mutation = useMutation({
    mutationFn: async (data) => {
      const url = isEditing ? `/ratas/${editData._id}` : "/ratas";
      const method = isEditing ? api.put : api.post;
      const response = await method(url, data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries(["ratas"]);
      toast.success(data.message || `RATA ${isEditing ? "updated" : "created"} successfully`);
      onClose();
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.error || error.message || "An error occurred";
      toast.error(errorMessage);
    }
  });

  const onSubmit = (data) => {
    mutation.mutate(data);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  // Calculate annual RATA
  const annualRata = (parseFloat(watchedValues.RATA) || 0) * 12;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle 
        sx={{ 
          background: 'linear-gradient(135deg, #264524 0%, #375e38 100%)',
          color: 'white',
          py: 3,
          position: 'relative'
        }}
      >
        <Box>
          <Typography variant="subtitle1" fontWeight="bold" component="div">
            {isEditing ? "Edit RATA Rate" : "Add New RATA Rate"}
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }} component="div">
            Configure Representation and Transportation Allowance by salary grade
          </Typography>
          {isEditing && (
            <Chip
              label={`SG-${editData.SG}`}
              size="small"
              sx={{
                position: 'absolute',
                top: 16,
                right: 16,
                backgroundColor: 'rgba(255,255,255,0.2)',
                color: 'white'
              }}
            />
          )}
        </Box>
      </DialogTitle>

      <DialogContent 
        dividers 
        sx={{ 
          p: 0,
          backgroundColor: '#f8f9fa'
        }}
      >
        <Box sx={{ p: 3 }}>
          {/* Information Alert */}
          <Alert severity="info" sx={{ mb: 3 }}>
            RATA (Representation and Transportation Allowance) is a monthly allowance based on salary grade. 
            Higher grades typically receive higher RATA amounts.
          </Alert>

          <Grid container spacing={3}>
            {/* Salary Grade Selection */}
            <Grid item xs={12} md={6}>
              <Controller
                name="SG"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label="Salary Grade"
                    fullWidth
                    variant="outlined"
                    error={!!errors.SG}
                    helperText={errors.SG?.message}
                    InputProps={{
                      startAdornment: (
                        <Box sx={{ mr: 1, color: '#264524' }}>
                          <MdGrade />
                        </Box>
                      )
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: '#264524',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#264524',
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#264524',
                      },
                    }}
                  >
                    {salaryGrades.map((grade) => (
                      <MenuItem key={grade} value={grade}>
                        SG-{grade}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>

            {/* RATA Amount */}
            <Grid item xs={12} md={6}>
              <Controller
                name="RATA"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="RATA Amount (Monthly)"
                    fullWidth
                    variant="outlined"
                    error={!!errors.RATA}
                    helperText={errors.RATA?.message}
                    InputProps={{
                      inputComponent: PesoFormatCustom,
                      startAdornment: (
                        <InputAdornment position="start">
                          <Box sx={{ color: '#264524', display: 'flex', alignItems: 'center' }}>
                            <MdAttachMoney />
                          </Box>
                        </InputAdornment>
                      ),
                      style: { textAlign: 'right' }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&:hover fieldset': {
                          borderColor: '#264524',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#264524',
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: '#264524',
                      },
                    }}
                  />
                )}
              />
            </Grid>
          </Grid>

          {/* Calculation Summary */}
          {watchedValues.RATA > 0 && (
            <>
              <Divider sx={{ my: 3 }} />
              <Box 
                sx={{ 
                  p: 2, 
                  backgroundColor: alpha(theme.palette.success.main, 0.1),
                  borderRadius: 2,
                  border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
                }}
              >
                <Typography variant="h6" color="success.main" gutterBottom>
                  Calculation Summary
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Monthly RATA:
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      ₱{(parseFloat(watchedValues.RATA) || 0).toLocaleString('en-PH', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Annual RATA:
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      ₱{((parseFloat(watchedValues.RATA) || 0) * 12).toLocaleString('en-PH', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </>
          )}
        </Box>
      </DialogContent>

      <DialogActions 
        sx={{ 
          p: 3, 
          backgroundColor: '#ffffff',
          borderTop: '1px solid #e0e0e0'
        }}
      >
        <Button 
          onClick={handleClose} 
          variant="outlined"
          sx={{ mr: 2 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit(onSubmit)}
          variant="contained"
          disabled={mutation.isLoading}
          startIcon={mutation.isLoading ? null : isEditing ? <MdSave /> : <MdAdd />}
          sx={{
            background: 'linear-gradient(45deg, #264524 30%, #375e38 90%)',
            boxShadow: '0 3px 5px 2px rgba(38, 69, 36, 0.3)',
            px: 4,
            py: 1.5,
            '&:hover': {
              background: 'linear-gradient(45deg, #1a2f1a 30%, #2a4a2b 90%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 10px 4px rgba(38, 69, 36, 0.3)'
            },
            '&:disabled': {
              background: 'linear-gradient(45deg, #ccc 30%, #999 90%)'
            }
          }}
        >
          {mutation.isLoading ? "Saving..." : isEditing ? "Update RATA" : "Save RATA"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnhancedRataDialog;

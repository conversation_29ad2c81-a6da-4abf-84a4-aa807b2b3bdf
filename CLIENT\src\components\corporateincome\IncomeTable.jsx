import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useUser } from "../../context/UserContext";
import api from "../../config/api";
import PropTypes from 'prop-types';
import {
  Box, Paper, Typography, Button, Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, TextField, IconButton,
  CircularProgress, Dialog, DialogActions, DialogContent,
  DialogContentText, DialogTitle, FormControl, InputLabel,
  Select, MenuItem, Switch, FormControlLabel, TableFooter,
  Chip, Tooltip, Card, CardContent, Grid, Divider, Fade, Zoom,
  Alert, Accordion, AccordionSummary, AccordionDetails, Menu
} from "@mui/material";
import {
  Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon,
  Save as SaveIcon, Cancel as CancelIcon, Settings as SettingsIcon,
  Refresh as RefreshIcon, GetApp as GetAppIcon, Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon, AccountBalance as AccountBalanceIcon,
  Info as InfoIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon
} from "@mui/icons-material";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { NumericFormat } from "react-number-format";

// Add this import for the sticky buttons
import StickyButtons from "../mooe/StickyButtons";

const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
  const { onChange, value, ...other } = props;

  // Ensure the value is properly formatted for display
  const displayValue = React.useMemo(() => {
    if (value === '' || value === null || value === undefined) return '';
    const numValue = Number(value);
    return isNaN(numValue) ? '' : numValue.toString();
  }, [value]);

  return (
    <NumericFormat
      {...other}
      value={displayValue}
      getInputRef={ref}
      thousandSeparator=","
      decimalScale={2}
      fixedDecimalScale={false} // Allow users to enter without decimals, but support them
      allowNegative={false} // Prevent negative values
      allowLeadingZeros={false}
      valueIsNumericString={true}
      placeholder="0.00"
      onValueChange={(values, sourceInfo) => {
        // Only trigger onChange if the value actually changed
        // and avoid triggering on format-only changes
        if (sourceInfo.source !== 'prop' && onChange) {
          const numericValue = values.value || "";
          onChange({
            target: {
              name: props.name,
              value: numericValue,
            },
          });
        }
      }}
    />
  );
});

// Add a prop to receive the callback function
const IncomeTable = ({ onGrandTotalUpdate }) => {
  const queryClient = useQueryClient();
  const { currentUser } = useUser();
  const [editingItemId, setEditingItemId] = useState(null);
  const [editFormData, setEditFormData] = useState({});
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState("");
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [newItem, setNewItem] = useState({
    name: "",
    particulars: "",
    amount: "",
    category: { name: "" }
  });
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  
  // Discount settings state
  const [discountSettingsOpen, setDiscountSettingsOpen] = useState(false);
  const [discountType, setDiscountType] = useState("percentage");
  const [discountValue, setDiscountValue] = useState(10);
  const [applyDiscount, setApplyDiscount] = useState(true);
  const [savingSettings, setSavingSettings] = useState(false);

  // Add these state variables at the beginning of your component
  const [fiscalYear, setFiscalYear] = useState("");
  const [budgetType, setBudgetType] = useState("");

  // Add this at the beginning of your component
  const [activeSettings, setActiveSettings] = useState(null);

  // Add this state to track which fields have been modified by the user
  const [userModifiedFields, setUserModifiedFields] = useState({});

  // Add this state to track unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Add state for new subcategory inputs
  const [newSubcategories, setNewSubcategories] = useState({});
  const [addingToCategoryId, setAddingToCategoryId] = useState(null);

  // Enhanced UI/UX state
  const [autoSave, setAutoSave] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [compactView, setCompactView] = useState(false);
  const [showSummaryCard, setShowSummaryCard] = useState(true);
  const [exportMenuAnchor, setExportMenuAnchor] = useState(null);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [autoSaveTimer, setAutoSaveTimer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Handle expand/collapse all categories - moved after groupedItems calculation

  // Auto-save functionality will be added after handleSaveAll is defined

  // Refresh data
  const handleRefresh = useCallback(() => {
    queryClient.invalidateQueries(["income-items", fiscalYear, budgetType]);
    queryClient.invalidateQueries(["income-categories"]);
    toast.info("Data refreshed");
  }, [queryClient, fiscalYear, budgetType]);

  // Enhanced export functionality - moved after groupedItems definition

  // Fetch active settings
  const fetchActiveSettings = useCallback(async () => {
    try {
      const response = await api.get("/settings/active");
      if (response.data) {
        setActiveSettings(response.data);
        // Update fiscalYear and budgetType from active settings
        setFiscalYear(response.data.fiscalYear);
        setBudgetType(response.data.budgetType);
      } else {
        toast.error("No active fiscal year settings found");
      }
    } catch (error) {
      console.error("Error fetching active settings:", error);
      toast.error("Failed to fetch active settings");
    }
  }, []);

  // Call this in useEffect
  useEffect(() => {
    fetchActiveSettings();
  }, [fetchActiveSettings]);

  // Get active fiscal year and budget type from context or props
  // If not available, use defaults
  useEffect(() => {
    if (!fiscalYear) {
      setFiscalYear(currentUser?.fiscalYear || new Date().getFullYear().toString());
    }
    if (!budgetType) {
      setBudgetType(currentUser?.budgetType || "REGULAR");
    }
  }, [currentUser, fiscalYear, budgetType]);

  useEffect(() => {
    // Debug API routes
    const debugApiRoutes = async () => {
      try {
        console.log("Checking available API endpoints...");
        
        // Check if we can access the server at all
        try {
          const response = await api.get("/");
          console.log("Server root endpoint works:", response.data);
        } catch (error) {
          console.log("Server root endpoint failed:", error.message);
        }
        
        // Check if the income endpoint exists
        try {
          const response = await api.get("/income");
          console.log("Income endpoint works:", response.data);
        } catch (error) {
          console.log("Income endpoint failed:", error.message);
        }
        
        // Check if we need to use a different endpoint
        try {
          const response = await api.get("/income");
          console.log("API income endpoint works:", response.data);
        } catch (error) {
          console.log("API income endpoint failed:", error.message);
        }
      } catch (error) {
        console.error("Debug API routes failed:", error);
      }
    };
    
    debugApiRoutes();
  }, []);

  // Fetch discount settings
  const { data: discountSettings, isLoading: loadingSettings } = useQuery({
    queryKey: ["discount-settings", fiscalYear, budgetType],
    queryFn: async () => {
      try {
        console.log(`Fetching settings for FY: ${fiscalYear}, Budget: ${budgetType}`);
        const response = await api.get("/api/discount-settings", {
          params: { fiscalYear, budgetType }
        });
        console.log("Settings response:", response.data);
        return response.data;
      } catch (error) {
        console.error("Error fetching discount settings:", error);
        return null;
      }
    },
    staleTime: 0, // Always fetch fresh data
    cacheTime: 0, // Don't cache the data
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchOnMount: true, // Refetch when component mounts
  });

  // Apply fetched settings to state when data changes
  useEffect(() => {
    if (discountSettings) {
      console.log("Applying settings:", discountSettings);
      setDiscountType(discountSettings.discountType || "percentage");
      setDiscountValue(discountSettings.discountValue || 10);
      setApplyDiscount(discountSettings.applyDiscount !== undefined ? discountSettings.applyDiscount : true);
    }
  }, [discountSettings]);

  // Update discount settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (data) => {
      console.log("Saving settings:", data);
      const response = await api.put("/api/discount-settings", data);
      return response.data;
    },
    onSuccess: () => {
      // Force refetch the settings
      queryClient.invalidateQueries(["discount-settings", fiscalYear, budgetType]);
      queryClient.refetchQueries(["discount-settings", fiscalYear, budgetType]);
      
      toast.success("Discount settings saved successfully");
      setDiscountSettingsOpen(false);
      setSavingSettings(false);
    },
    onError: (error) => {
      console.error("Error saving settings:", error);
      toast.error(`Error saving discount settings: ${error.message}`);
      setSavingSettings(false);
    }
  });

  // Handle opening and closing the discount settings dialog
  const handleOpenDiscountSettings = () => setDiscountSettingsOpen(true);
  const handleCloseDiscountSettings = () => setDiscountSettingsOpen(false);

  // Handle discount type change
  const handleDiscountTypeChange = (event) => {
    setDiscountType(event.target.value);
  };

  // Handle discount value change
  const handleDiscountValueChange = (event) => {
    setDiscountValue(Number(event.target.value));
  };

  // Handle apply discount toggle
  const handleApplyDiscountChange = (event) => {
    setApplyDiscount(event.target.checked);
  };

  // Save discount settings to database
  const handleSaveDiscountSettings = () => {
    setSavingSettings(true);
    updateSettingsMutation.mutate({
      fiscalYear,
      budgetType,
      discountType,
      discountValue,
      applyDiscount
    });
  };

  // Format number for display
  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num).replace('PHP', '').trim();
  };

  // Fetch income items
  const { 
    data: incomeItems = [], 
    isLoading,
    refetch 
  } = useQuery({
    queryKey: ["income-items", fiscalYear, budgetType],
    queryFn: async () => {
      try {
        console.log("Fetching income items with params:", { fiscalYear, budgetType });
        const response = await api.get("/income", {
          params: { fiscalYear, budgetType }
        });
        console.log("Income API Response:", response.data);
        return Array.isArray(response.data) ? response.data : [];
      } catch (error) {
        console.error("Error fetching income items:", error);
        toast.error(`Error fetching income items: ${error.message}`);
        return [];
      }
    },
    enabled: !!fiscalYear && !!budgetType,
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });

  // Fetch income categories
  const { 
    data: incomeCategories = [], 
    isLoading: loadingCategories,
  } = useQuery({
    queryKey: ["income-categories"],
    queryFn: async () => {
      try {
        const response = await api.get("/income-categories");
        console.log("Income categories API response:", response.data);
        
        // Determine the correct data structure
        let categoriesArray = [];
        if (Array.isArray(response.data)) {
          categoriesArray = response.data;
        } else if (response.data && Array.isArray(response.data.categories)) {
          categoriesArray = response.data.categories;
        }
        
        console.log("Processed categories array:", categoriesArray);
        return categoriesArray;
      } catch (error) {
        console.error("Error fetching income categories:", error);
        toast.error(`Error fetching income categories: ${error.message}`);
        return [];
      }
    }
  });

  // Add state for storing amount inputs for subcategories
  const [subcategoryAmounts, setSubcategoryAmounts] = useState({});

  // Handle amount change for a subcategory
  const handleSubcategoryAmountChange = (categoryId, subcategoryName, value) => {
    const key = `${categoryId}-${subcategoryName}`;
    setSubcategoryAmounts(prev => ({
      ...prev,
      [key]: value
    }));
    
    // Mark this field as modified by the user
    setUserModifiedFields(prev => ({
      ...prev,
      [key]: true
    }));
    
    // Set hasUnsavedChanges to true
    setHasUnsavedChanges(true);
  };

  // Calculate subtotal for a category
  const calculateCategorySubtotal = (categoryId, subcategories) => {
    if (!Array.isArray(subcategories)) return 0;

    return subcategories.reduce((total, subcategory) => {
      const key = `${categoryId}-${subcategory}`;
      const amount = parseFloat(subcategoryAmounts[key]) || 0;
      return total + amount;
    }, 0);
  };

  // Calculate subtotal for a category including "Others" items in Miscellaneous Income
  // Note: This function is not currently used but kept for potential future use
  const calculateCategorySubtotalWithOthers = (categoryId, subcategories, categoryName) => {
    if (!Array.isArray(subcategories)) return 0;

    let total = subcategories.reduce((sum, subcategory) => {
      const key = `${categoryId}-${subcategory}`;
      const amount = parseFloat(subcategoryAmounts[key]) || 0;
      return sum + amount;
    }, 0);

    // Note: Others integration is now handled in the groupedItems calculation
    // to avoid circular dependency issues

    return total;
  };

  // Save amount for a subcategory to income table
  const saveIncomeAmountMutation = useMutation({
    mutationFn: async (data) => {
      try {
        console.log("Sending data to server:", data);
        const response = await api.post("/income", data);
        console.log("Server response:", response.data);
        return response.data;
      } catch (error) {
        console.error("Error saving income amount:", error);
        toast.error(`Error saving income amount: ${error.message}`);
        throw error;
      }
    },
    onSuccess: () => {
      // Refresh the data but don't reset the form
      queryClient.invalidateQueries(["income-items", fiscalYear, budgetType]);
      // Don't clear subcategoryAmounts here
    }
  });

  // Update existing income entry - make sure it's properly implemented
  const updateIncomeMutation = useMutation({
    mutationFn: ({ id, data }) => {
      console.log("Updating income with ID:", id, "Data:", data);
      return api.put(`/income/${id}`, data);
    },
    onSuccess: (data) => {
      console.log("Income updated successfully:", data);
      queryClient.invalidateQueries(["income-items", fiscalYear, budgetType]);
      toast.success("Income amount updated successfully");
    },
    onError: (error) => {
      console.error("Error updating income:", error);
      toast.error(`Error updating income amount: ${error.message}`);
    }
  });

  const handleSaveSubcategoryAmount = (category, subcategoryName) => {
    const key = `${category._id}-${subcategoryName}`;
    const amount = subcategoryAmounts[key] || 0;
    
    console.log("Saving subcategory amount:", {
      key,
      amount,
      category,
      subcategoryName
    });
    
    // Check if this subcategory already has an entry
    const existingItem = incomeItems.find(item => 
      (item.incomecategory === category._id || 
       (item.incomecategory && item.incomecategory._id === category._id)) && 
      item.subcategory === subcategoryName
    );
    
    console.log("Existing item check:", {
      existingItem,
      incomeItems: incomeItems.length
    });
    
    // Prepare data object - ensure category is stored consistently
    const categoryName = category.incomeCategoryName ? category.incomeCategoryName.trim() : "Unknown Category";
    
    const incomeData = {
      particulars: subcategoryName,
      cost: Number(amount) || 0,
      incomecategory: category._id,
      subcategory: subcategoryName,
      category: categoryName, // Use trimmed category name
      amount: Number(amount) || 0,
      fiscalYear: fiscalYear,
      budgetType: budgetType,
      region: currentUser?.Region || "NCR",
      processBy: currentUser ? `${currentUser.FirstName} ${currentUser.LastName}` : "User",
      processDate: new Date()
    };
    
    console.log("Prepared income data:", incomeData);
    
    if (existingItem) {
      console.log("Updating existing item:", existingItem._id);
      // Update existing entry
      updateIncomeMutation.mutate({
        id: existingItem._id,
        data: incomeData
      }, {
        onSuccess: (data) => {
          console.log("Update successful, response:", data);
          // Add toast notification here
          toast.success("Income amount updated successfully", {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true
          });
        }
      });
    } else {
      console.log("Creating new item");
      // Create new entry
      saveIncomeAmountMutation.mutate(incomeData, {
        onSuccess: (data) => {
          console.log("Create successful, response:", data);
          // Add toast notification here
          toast.success("Income amount saved successfully", {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true
          });
        }
      });
    }
    
    // After successful save, we can clear the "modified" flag for this field
    // This allows future server updates to affect this field again
    setUserModifiedFields(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  // Add item mutation
  const addItemMutation = useMutation({
    mutationFn: (data) => api.post("/income-subcategories", data),
    onSuccess: () => {
      queryClient.invalidateQueries(["income-items"]);
      toast.success("Item added successfully");
      setIsAddingItem(false);
      setNewItem({
        name: "",
        particulars: "",
        amount: "",
        category: { name: "" }
      });
    },
    onError: (error) => {
      toast.error(`Error adding item: ${error.message}`);
    }
  });

  // Update item mutation
  const updateItemMutation = useMutation({
    mutationFn: (data) => api.put(`/income-subcategories/${data._id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries(["income-items"]);
      toast.success("Item updated successfully");
      setEditingItemId(null);
    },
    onError: (error) => {
      toast.error(`Error updating item: ${error.message}`);
    }
  });

  // Delete item mutation
  const deleteItemMutation = useMutation({
    mutationFn: (id) => api.delete(`/income-subcategories/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries(["income-items"]);
      toast.success("Item deleted successfully");
      setDeleteConfirmOpen(false);
      setItemToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting item: ${error.message}`);
    }
  });

  // Add category mutation
  const addCategoryMutation = useMutation({
    mutationFn: (data) => api.post("/categories", data),
    onSuccess: () => {
      queryClient.invalidateQueries(["income-items"]);
      toast.success("Category added successfully");
      setIsAddingCategory(false);
      setNewCategoryName("");
    },
    onError: (error) => {
      toast.error(`Error adding category: ${error.message}`);
    }
  });

  // Group items by category with special handling for ISF and discounts
  const groupedItems = React.useMemo(() => {
    // Create a map to store categories and their items
    const categoryMap = new Map();
    
    // Add all categories from the database first
    if (Array.isArray(incomeCategories)) {
      incomeCategories.forEach(category => {
        if (!category || !category.incomeCategoryName || category.incomeCategoryName.toLowerCase() === 'test') return; // Skip test categories
        
        const trimmedName = category.incomeCategoryName.trim();
        categoryMap.set(trimmedName, {
          items: [],
          subtotal: 0,
          isMainCategory: true,
          subcategories: category.incomeSubcategoryName || [],
          categoryId: category._id
        });
      });
    }
    
    // Track ISF total and Current Account for discount calculation
    let isfTotal = 0;
    let currentAccountTotal = 0;
    let hasIsfCategory = false;

    // Add items to their respective categories
    if (Array.isArray(incomeItems)) {
      incomeItems.forEach(item => {
        if (!item.category) return;

        // Handle both object and string formats for category
        const categoryName = typeof item.category === 'object' ? item.category.name : item.category;
        if (!categoryName) return;

        const trimmedName = categoryName.trim();
        if (!categoryMap.has(trimmedName)) {
          // If category doesn't exist yet, create it
          categoryMap.set(trimmedName, {
            items: [],
            subtotal: 0,
            isMainCategory: true,
            subcategories: []
          });
        }

        // Add item to its category
        const category = categoryMap.get(trimmedName);
        category.items.push(item);
        category.subtotal += Number(item.amount) || 0;

        // Track ISF total and Current Account for discount calculation
        if (trimmedName.includes("Irrigation Service Fees") && !trimmedName.includes("Net")) {
          hasIsfCategory = true;
          isfTotal += Number(item.amount) || 0;

          // Track Current Account specifically for discount calculation
          if (item.subcategory === "Current Account") {
            currentAccountTotal += Number(item.amount) || 0;
          }
        }
      });
    }
    
    // Always add Net ISF and discount categories if ISF category exists
    if (hasIsfCategory || true) { // Force add for now
      // Calculate discount on Current Account only (not entire ISF)
      let discountAmount = 0;
      if (applyDiscount) {
        if (discountType === "percentage") {
          discountAmount = currentAccountTotal * (discountValue / 100);
        } else { // fixed amount
          discountAmount = Math.min(discountValue, currentAccountTotal); // Can't discount more than Current Account total
        }
      }

      // Add discount as a special category if discount is applied
      if (applyDiscount) {
        // Format the discount label based on discount type
        const discountLabel = discountType === "percentage"
          ? `Less: ${discountValue}% Discount on Current Account`
          : `Less: ${formatNumber(discountValue)} Discount on Current Account`;

        categoryMap.set(discountLabel, {
          items: [],
          subtotal: discountAmount,
          isDiscount: true,
          isMainCategory: true,
          subcategories: []
        });
      }

      // Always add Net ISF (total ISF minus discount on Current Account)
      const netIsfAmount = isfTotal - (applyDiscount ? discountAmount : 0);
      categoryMap.set("Net Irrigation Service Fees", {
        items: [],
        subtotal: netIsfAmount,
        isMainCategory: true,
        subcategories: [],
        isNetIsf: true
      });
    }
    
    // Define the exact order of categories as specified
    const categoryOrder = [
      "Irrigation Service Fees",
      "Less: 10% Discount on ISF",
      "Net Irrigation Service Fees",
      "Rent Income",  // Make sure this matches exactly with the data
      "Water Service Fee",
      "Miscellaneous Income"
      // "Others" removed - now handled as subcategory under Miscellaneous Income
    ];
    
    // Define subcategory order for ISF
    const isfSubcategories = [
      "Current Account",
      "Back Accounts"
    ];
    
    // Define subcategory order for Water Service Fee
    const waterServiceFeeSubcategories = [
      "Aya PSP (CO)",
      "NPC Energy Delivery (CO)",
      "Sun Asia Floating Solar (CO)",
      "DPJ Water Service Fee (Power at CAR -UCRIS)",
      "Mindoro Grid Corporation (Region 1)",
      "BHEP Power Generation (MARIIS)",
      "MHEPP (MARIIS)",
      "SN Aboitiz Power Inc. (MARIIS)",
      "First Gen. Hydro Electric Plant (UPRIIS)",
      "Fresh River Lakes Corp (FRLC) (UPRIIS)",
      "Phil Hydro (UPRIIS & Region 3)",
      "Bulk Water (Philhydro - Region 3)",
      "Balingasag Crusher (Region 10)",
      "Metropac (Region 10)",
      "Water Fees (Mcdo) - Region 10",
      "Eurohydro (Region 12)",
      "Global Banana Corp. (Region 12)",
      "Palm Oil (Region 12)"
    ];
    
    // Define subcategory order for Miscellaneous Income
    const miscellaneousSubcategories = [
      "Bid Forms",
      "Certification Fee/Verification Fee",
      "CIP/CIS/RIS Amortization & Equity",
      "Drainage Fee",
      "Interest Income",
      "Land Development",
      "Lodging",
      "Management Fees",
      "NIA Facilities",
      "Penalty on Back Accounts",
      "Pump Amortization & Equity",
      "Sales of Non Performing Assets",
      "Sales of Scrap",
      "Others"
    ];
    
    // Define subcategory order for Others
    const othersSubcategories = [
      "Balingasag - Crusher",
      "Drying Fee",
      "Filing Fee/ Inspection",
      "Fishery/Gate Pass/Fishcage Permit",
      "FTC/PL Resort (UPRIIS)",
      "Global (Banana Co.)",
      "Hydro/Portable Water",
      "Hydropower (CASECNAN)",
      "Income from NIA housing",
      "Income from RTC (Rental)",
      "Irrigation Development Cost",
      "Lab Analysis",
      "Lapanday Banana Plantation",
      "Lease property/Drainage Fee",
      "Mindoro Grid Corporation",
      "Other Fines and Penalties",
      "Printing & Xerox",
      "Space Rental of Pavilion/Building",
      "Use of Service Roads",
      "Water/Electrical/Housing",
      "Water Service Fee",
      "Miscellaneous/Others"
    ];
    
    // Update subcategories for specific categories
    if (categoryMap.has("Irrigation Service Fees")) {
      categoryMap.get("Irrigation Service Fees").subcategories = isfSubcategories;
    }
    
    if (categoryMap.has("Water Service Fee")) {
      categoryMap.get("Water Service Fee").subcategories = waterServiceFeeSubcategories;
    }
    
    if (categoryMap.has("Miscellaneous Income")) {
      categoryMap.get("Miscellaneous Income").subcategories = miscellaneousSubcategories;
    }
    
    if (categoryMap.has("Others")) {
      categoryMap.get("Others").subcategories = othersSubcategories;
    }
    
    // Add debug log to see actual category names
    console.log("Actual category names in data:", Array.from(categoryMap.keys()));
    
    // Convert map to ordered object based on the specified category order
    const result = {};
    categoryOrder.forEach(categoryName => {
      // For the discount category, we need to find it by partial match
      if (categoryName === "Less: 10% Discount on ISF") {
        const discountKey = Array.from(categoryMap.keys()).find(key => key.includes("Less:") && key.includes("Discount"));
        if (discountKey) {
          result[discountKey] = categoryMap.get(discountKey);
        }
      }
      // For Rent Income, check for both "Rent Income" and "Rent  Income" (with double spaces)
      else if (categoryName === "Rent Income") {
        const rentKey = Array.from(categoryMap.keys()).find(key =>
          key === "Rent Income" || key === "Rent  Income" || key.includes("Rent") && key.includes("Income")
        );
        if (rentKey) {
          result[rentKey] = categoryMap.get(rentKey);
        }
      }
      // For Miscellaneous Income, add Others total to its subtotal and add Others as subcategory
      else if (categoryName === "Miscellaneous Income" && categoryMap.has(categoryName)) {
        const miscCategory = categoryMap.get(categoryName);
        const othersCategory = categoryMap.get("Others");

        // Add "Others" as a subcategory if it exists (regardless of amount)
        const updatedSubcategories = [...(miscCategory.subcategories || [])];
        if (othersCategory && !updatedSubcategories.includes("Others")) {
          updatedSubcategories.push("Others");
        }

        // Also add all the original Others subcategories to Miscellaneous Income
        if (othersCategory && othersCategory.subcategories) {
          othersCategory.subcategories.forEach(subcat => {
            if (!updatedSubcategories.includes(subcat)) {
              updatedSubcategories.push(subcat);
            }
          });
        }

        // Calculate the actual Others total from subcategoryAmounts
        let calculatedOthersTotal = 0;
        if (othersCategory && othersCategory.subcategories) {
          calculatedOthersTotal = othersCategory.subcategories.reduce((sum, subcat) => {
            const key = `${othersCategory.categoryId}-${subcat}`;
            const amount = parseFloat(subcategoryAmounts[key]) || 0;
            return sum + amount;
          }, 0);
        }

        result[categoryName] = {
          ...miscCategory,
          subtotal: miscCategory.subtotal + calculatedOthersTotal,
          subcategories: updatedSubcategories,
          includesOthers: true,
          othersTotal: calculatedOthersTotal
        };
      }
      // For other categories, exact match (but exclude Others since it's now under Miscellaneous Income)
      else if (categoryMap.has(categoryName) && categoryName !== "Others") {
        result[categoryName] = categoryMap.get(categoryName);
      }
    });

    // Add any remaining categories not in the predefined order (except Others and discount categories)
    // Others is now handled as a subcategory under Miscellaneous Income
    categoryMap.forEach((value, key) => {
      if (!result[key] && !key.includes("Less:") && !key.includes("Net Irrigation") && key !== "Others") {
        result[key] = value;
      }
    });
    
    console.log("Grouped items:", result);
    return result;
  }, [incomeItems, incomeCategories, discountType, discountValue, applyDiscount, subcategoryAmounts]);

  // Calculate grand total - only include specific categories for Income Grand Total
  const grandTotal = useMemo(() => {
    const allowedCategories = [
      "Net Irrigation Service Fees",
      "Rent Income",
      "Rent  Income", // Handle double space version
      "Water Service Fee",
      "Miscellaneous Income"
    ];

    // Debug: Log all available categories and their amounts
    console.log("IncomeTable: Available categories:", Object.keys(groupedItems));
    console.log("IncomeTable: All categories with amounts:",
      Object.entries(groupedItems).map(([name, cat]) => ({ name, amount: cat.subtotal || 0 }))
    );

    let total = 0;
    let includedCategories = [];

    Object.entries(groupedItems).forEach(([categoryName, category]) => {
      if (allowedCategories.includes(categoryName)) {
        const amount = category.subtotal || 0;
        total += amount;
        includedCategories.push({ name: categoryName, amount });
        console.log(`IncomeTable: ✅ Including ${categoryName} with amount ₱${amount.toLocaleString()}`);
      } else {
        console.log(`IncomeTable: ❌ Excluding ${categoryName} (₱${(category.subtotal || 0).toLocaleString()}) from grand total`);
      }
    });

    console.log("IncomeTable: Included categories summary:", includedCategories);
    console.log("IncomeTable: Final grand total:", total);

    // Manual verification based on actual data from logs
    const expectedTotal = 1400000 + 46778000 + 0 + 73166000; // Based on console logs
    console.log("IncomeTable: Expected total based on actual data:", expectedTotal);
    console.log("IncomeTable: Breakdown - Net ISF: 1,400,000 + Rent: 46,778,000 + Water: 0 + Misc: 73,166,000");

    return total;
  }, [groupedItems]);

  // Enhanced export functionality - now after groupedItems definition
  const handleExportToExcel = useCallback(() => {
    const exportData = [];

    Object.entries(groupedItems || {}).forEach(([categoryName, category]) => {
      // Add category row
      exportData.push({
        'Category': categoryName,
        'Subcategory': '',
        'Amount': category.subtotal || 0,
        'Type': 'Category Total',
        'Fiscal Year': fiscalYear,
        'Budget Type': budgetType,
        'Last Updated': lastSaved ? new Date(lastSaved).toLocaleString() : 'Never'
      });

      // Add subcategory rows
      if (category.subcategories && category.subcategories.length > 0) {
        category.subcategories.forEach(subcategory => {
          const key = `${category.categoryId}-${subcategory}`;
          const amount = subcategoryAmounts[key] || 0;
          exportData.push({
            'Category': categoryName,
            'Subcategory': subcategory,
            'Amount': amount,
            'Type': 'Subcategory',
            'Fiscal Year': fiscalYear,
            'Budget Type': budgetType,
            'Last Updated': lastSaved ? new Date(lastSaved).toLocaleString() : 'Never'
          });
        });
      }
    });

    // Create CSV content
    const headers = Object.keys(exportData[0] || {});
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Income_Table_${fiscalYear}_${budgetType}_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    toast.success("Income data exported successfully!");
    setExportMenuAnchor(null);
  }, [groupedItems, subcategoryAmounts, fiscalYear, budgetType, lastSaved]);

  // Expand/collapse functions are now inline to avoid initialization issues

  const handleAddItem = (categoryName) => {
    const itemToAdd = {
      ...newItem,
      category: { name: categoryName },
      amount: Number(newItem.amount) || 0,
      fiscalYear: new Date().getFullYear().toString(),
      budgetType: "Initial",
      region: currentUser?.Region,
      processBy: currentUser ? `${currentUser.FirstName} ${currentUser.LastName}` : "User",
      processDate: new Date()
    };
    
    addItemMutation.mutate(itemToAdd);
  };

  const handleEditClick = (item) => {
    setEditingItemId(item._id);
    setEditFormData({...item});
  };

  const handleEditChange = (e, field) => {
    setEditFormData({
      ...editFormData,
      [field]: field === 'amount' ? Number(e.target.value) : e.target.value
    });
  };

  const handleSaveEdit = () => {
    updateItemMutation.mutate(editFormData);
  };

  const handleCancelEdit = () => {
    setEditingItemId(null);
  };

  const handleDeleteClick = (itemId) => {
    setItemToDelete(itemId);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (itemToDelete) {
      deleteItemMutation.mutate(itemToDelete);
    }
  };

  const handleAddCategory = () => {
    if (!newCategoryName.trim()) {
      toast.error("Category name cannot be empty");
      return;
    }
    
    addCategoryMutation.mutate({
      name: newCategoryName,
      type: "income"
    });
  };

  // Check for duplicates
  useEffect(() => {
    if (incomeItems.length > 0) {
      console.log("Income items:", incomeItems);
      // Look for duplicates - fix the category name access
      const duplicates = incomeItems
        .map(item => item.category?.name || item.category) // Handle both object and string formats
        .filter(Boolean) // Filter out undefined/null values
        .filter((name, index, array) => array.indexOf(name) !== index);
      if (duplicates.length > 0) {
        console.log("Duplicate categories:", duplicates);
      }
    }
  }, [incomeItems]);

  // Add this function to your component
  const testDirectSave = async () => {
    try {
      // Create a test income entry
      const testData = {
        particulars: "Test Income Entry",
        cost: 1000,
        incomecategory: "65f2d5b1e5c5a80b8c8a1234", // Replace with a valid category ID
        subcategory: "Test Subcategory",
        category: "Test Category",
        amount: 1000,
        fiscalYear: fiscalYear,
        budgetType: budgetType,
        region: "NCR",
        processBy: "Test User",
        processDate: new Date()
      };
      
      console.log("TEST: Sending direct test data:", testData);
      
      // Make a direct API call
      const response = await api.post("/income", testData);
      
      console.log("TEST: Direct API response:", response.data);
      
      // Refetch the data
      queryClient.invalidateQueries(["income-items", fiscalYear, budgetType]);
      toast.success("Test income entry created successfully");
    } catch (error) {
      console.error("TEST: Direct API error:", error);
      console.error("TEST: Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      toast.error(`Test failed: ${error.message}`);
    }
  };

  // Modify the useEffect for incomeItems to respect user modifications
  useEffect(() => {
    if (Array.isArray(incomeItems) && incomeItems.length > 0) {
      // Initialize subcategoryAmounts with existing values from incomeItems
      const initialAmounts = {};
      
      incomeItems.forEach(item => {
        if (item.incomecategory && item.subcategory) {
          const categoryId = item.incomecategory._id || item.incomecategory;
          const key = `${categoryId}-${item.subcategory}`;
          
          // Only update if the user hasn't modified this field
          if (!userModifiedFields[key]) {
            // Format the value to ensure proper decimal display
            const value = item.amount || item.cost || 0;
            initialAmounts[key] = Number(value).toString();
          }
        }
      });
      
      // Update state, preserving user-modified values
      setSubcategoryAmounts(prev => {
        const newState = { ...prev };
        
        // Only update fields that haven't been modified by the user
        Object.keys(initialAmounts).forEach(key => {
          if (!userModifiedFields[key]) {
            newState[key] = initialAmounts[key];
          }
        });
        
        return newState;
      });
    }
  }, [incomeItems, userModifiedFields]);

  // Add a function to save all changes
  const handleSaveAll = () => {
    // Get all modified fields
    const modifiedKeys = Object.keys(userModifiedFields);
    
    if (modifiedKeys.length === 0) {
      toast.info("No changes to save");
      return;
    }
    
    // Create an array of promises for all save operations
    const savePromises = modifiedKeys.map(key => {
      // Parse the key to get categoryId and subcategoryName
      const [categoryId, subcategoryName] = key.split('-');
      
      // Find the category object
      const category = incomeCategories.find(cat => cat._id === categoryId);
      if (!category) return null;
      
      const amount = subcategoryAmounts[key] || 0;
      
      // Check if this subcategory already has an entry
      const existingItem = incomeItems.find(item => 
        (item.incomecategory === categoryId || 
         (item.incomecategory && item.incomecategory._id === categoryId)) && 
        item.subcategory === subcategoryName
      );
      
      // Prepare data object
      const categoryName = category.incomeCategoryName ? category.incomeCategoryName.trim() : "Unknown Category";
      
      const incomeData = {
        particulars: subcategoryName,
        cost: Number(amount) || 0,
        incomecategory: categoryId,
        subcategory: subcategoryName,
        category: categoryName,
        amount: Number(amount) || 0,
        fiscalYear: fiscalYear,
        budgetType: budgetType,
        region: currentUser?.Region || "NCR",
        processBy: currentUser ? `${currentUser.FirstName} ${currentUser.LastName}` : "User",
        processDate: new Date()
      };
      
      // Return a promise for this save operation
      if (existingItem) {
        return api.put(`/income/${existingItem._id}`, incomeData);
      } else {
        return api.post("/income", incomeData);
      }
    }).filter(Boolean); // Remove any null promises
    
    // Execute all save operations
    Promise.all(savePromises)
      .then(() => {
        toast.success("All changes saved successfully");
        // Clear all modified flags
        setUserModifiedFields({});
        // Refresh data
        queryClient.invalidateQueries(["income-items", fiscalYear, budgetType]);
        // Reset unsaved changes flag
        setHasUnsavedChanges(false);
      })
      .catch(error => {
        console.error("Error saving changes:", error);
        toast.error("Error saving changes");
      });
  };
  
  // Add a function to clear unsaved changes
  const handleClearUnsaved = () => {
    // Get all modified fields
    const modifiedKeys = Object.keys(userModifiedFields);
    
    if (modifiedKeys.length === 0) {
      toast.info("No unsaved changes to clear");
      return;
    }
    
    // Create a new subcategoryAmounts object without the unsaved changes
    const newSubcategoryAmounts = { ...subcategoryAmounts };
    
    // For each modified field, reset to the original value from incomeItems
    modifiedKeys.forEach(key => {
      const [categoryId, subcategoryName] = key.split('-');
      
      // Find the original item
      const originalItem = incomeItems.find(item => 
        (item.incomecategory === categoryId || 
         (item.incomecategory && item.incomecategory._id === categoryId)) && 
        item.subcategory === subcategoryName
      );
      
      if (originalItem) {
        // Format the value to ensure proper decimal display
        const value = originalItem.amount || originalItem.cost || 0;
        newSubcategoryAmounts[key] = Number(value).toString();
      } else {
        // If there was no original item, remove this key
        delete newSubcategoryAmounts[key];
      }
    });
    
    // Update state
    setSubcategoryAmounts(newSubcategoryAmounts);
    setUserModifiedFields({});
    setHasUnsavedChanges(false);
    
    toast.info("Unsaved changes cleared");
  };

  // Auto-save functionality - now after handleSaveAll is defined
  useEffect(() => {
    if (autoSave && hasUnsavedChanges) {
      if (autoSaveTimer) clearTimeout(autoSaveTimer);

      const timer = setTimeout(() => {
        handleSaveAll();
        setLastSaved(new Date());
      }, 3000); // Auto-save after 3 seconds of inactivity

      setAutoSaveTimer(timer);

      return () => clearTimeout(timer);
    }
  }, [autoSave, hasUnsavedChanges]);

  // Handle adding a new subcategory input field
  const handleAddSubcategoryField = (categoryId, categoryName) => {
    setAddingToCategoryId(categoryId);
    setNewSubcategories(prev => ({
      ...prev,
      [categoryId]: ""
    }));
  };

  // Handle change in new subcategory name
  const handleNewSubcategoryChange = (categoryId, value) => {
    setNewSubcategories(prev => ({
      ...prev,
      [categoryId]: value
    }));
  };

  // Handle saving a new subcategory
  const handleSaveNewSubcategory = (categoryId, categoryName) => {
    const newSubcategoryName = newSubcategories[categoryId]?.trim();
    
    if (!newSubcategoryName) {
      toast.error("Subcategory name cannot be empty");
      return;
    }
    
    // Find the category in incomeCategories
    const category = incomeCategories.find(cat => cat._id === categoryId);
    
    if (!category) {
      toast.error("Category not found");
      return;
    }
    
    // Check if subcategory already exists
    if (category.incomeSubcategoryName && 
        category.incomeSubcategoryName.includes(newSubcategoryName)) {
      toast.error("This subcategory already exists");
      return;
    }
    
    // Add the subcategory to the category
    const updateCategoryMutation = useMutation({
      mutationFn: async () => {
        // Create updated subcategory list
        const updatedSubcategories = [
          ...(category.incomeSubcategoryName || []),
          newSubcategoryName
        ];
        
        // Update the category
        return api.put(`/income-categories/${categoryId}`, {
          ...category,
          incomeSubcategoryName: updatedSubcategories
        });
      },
      onSuccess: () => {
        toast.success("Subcategory added successfully");
        // Clear the input
        setNewSubcategories(prev => ({
          ...prev,
          [categoryId]: ""
        }));
        setAddingToCategoryId(null);
        // Refetch categories
        queryClient.invalidateQueries(["income-categories"]);
      },
      onError: (error) => {
        toast.error(`Error adding subcategory: ${error.message}`);
      }
    });
    
    updateCategoryMutation.mutate();
  };

  // Handle canceling adding a new subcategory
  const handleCancelAddSubcategory = (categoryId) => {
    setNewSubcategories(prev => ({
      ...prev,
      [categoryId]: ""
    }));
    setAddingToCategoryId(null);
  };

  // Update the parent component whenever the grand total changes
  useEffect(() => {
    if (onGrandTotalUpdate && typeof onGrandTotalUpdate === 'function') {
      console.log("IncomeTable: Calling onGrandTotalUpdate with:", grandTotal);
      onGrandTotalUpdate(grandTotal);
    }
  }, [grandTotal, onGrandTotalUpdate]);

  // Add an effect to initialize the grand total on mount
  useEffect(() => {
    // Calculate initial grand total
    const initialTotal = (incomeItems || []).reduce((sum, item) => sum + (Number(item.amount) || 0), 0);

    // Call the callback with the initial total
    if (initialTotal > 0 && onGrandTotalUpdate) {
      console.log("IncomeTable: Initializing with total:", initialTotal);
      onGrandTotalUpdate(initialTotal);
    }
  }, [incomeItems, onGrandTotalUpdate]);

  return (
    <>
      <Paper sx={{ p: 2, mb: 3 }}>
        {/* Enhanced Toolbar */}
        <Box sx={{ mb: 3 }}>
          {/* Top Row - Main Controls */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Box display="flex" alignItems="center" gap={2}>
              {/* Save and Clear buttons */}
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSaveAll}
                disabled={!hasUnsavedChanges}
                sx={{ minWidth: 120 }}
              >
                {hasUnsavedChanges ? 'Save Changes' : 'Saved'}
              </Button>

              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleClearUnsaved}
                disabled={!hasUnsavedChanges}
                sx={{ minWidth: 120 }}
              >
                Clear Changes
              </Button>

              {/* Auto-save toggle */}
              <FormControlLabel
                control={
                  <Switch
                    checked={autoSave}
                    onChange={(e) => {
                      setAutoSave(e.target.checked);
                      if (e.target.checked) {
                        toast.info("Auto-save enabled - changes will be saved automatically after 3 seconds", { autoClose: 3000 });
                      } else {
                        toast.info("Auto-save disabled - use Save button to save changes", { autoClose: 3000 });
                      }
                    }}
                    color="primary"
                  />
                }
                label={
                  <Box display="flex" alignItems="center" gap={1}>
                    <SaveIcon fontSize="small" color={autoSave ? "primary" : "disabled"} />
                    <Typography variant="body2" color={autoSave ? "primary" : "text.secondary"}>
                      Auto-save {autoSave ? "(ON)" : "(OFF)"}
                    </Typography>
                  </Box>
                }
              />

              {/* Last saved indicator */}
              {lastSaved && (
                <Chip
                  icon={<CheckCircleIcon />}
                  label={`Last saved: ${new Date(lastSaved).toLocaleTimeString()}`}
                  color="success"
                  variant="outlined"
                  size="small"
                />
              )}
            </Box>

            <Box display="flex" alignItems="center" gap={1}>
              {/* Export button */}
              <Tooltip title="Export data">
                <IconButton
                  onClick={(e) => setExportMenuAnchor(e.currentTarget)}
                  color="primary"
                >
                  <GetAppIcon />
                </IconButton>
              </Tooltip>

              {/* Refresh button */}
              <Tooltip title="Refresh data">
                <IconButton onClick={handleRefresh} color="primary">
                  <RefreshIcon />
                </IconButton>
              </Tooltip>

              {/* View toggle */}
              <Tooltip title="Toggle compact view">
                <IconButton
                  onClick={() => setCompactView(!compactView)}
                  color={compactView ? "primary" : "default"}
                >
                  <VisibilityIcon />
                </IconButton>
              </Tooltip>

              {/* Settings */}
              <Button
                variant="outlined"
                startIcon={<SettingsIcon />}
                onClick={handleOpenDiscountSettings}
              >
                Discount Settings
              </Button>
            </Box>
          </Box>

          {/* Second Row - Category Controls */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Box display="flex" alignItems="center" gap={2}>
              {/* Expand/Collapse Controls */}
              <Tooltip title="Expand all categories">
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const allCategories = Object.keys(groupedItems || {});
                    const expandedState = {};
                    allCategories.forEach(category => {
                      expandedState[category] = true;
                    });
                    setExpandedCategories(expandedState);
                  }}
                  startIcon={<ExpandMoreIcon />}
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Expand All
                </Button>
              </Tooltip>

              <Tooltip title="Collapse all categories">
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => {
                    const allCategories = Object.keys(groupedItems || {});
                    const collapsedState = {};
                    allCategories.forEach(category => {
                      collapsedState[category] = false;
                    });
                    setExpandedCategories(collapsedState);
                  }}
                  startIcon={<ExpandMoreIcon sx={{ transform: 'rotate(-90deg)' }} />}
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Collapse All
                </Button>
              </Tooltip>

              {/* Search */}
              <TextField
                size="small"
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                sx={{ width: 250 }}
                InputProps={{
                  startAdornment: (
                    <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                      <InfoIcon fontSize="small" color="action" />
                    </Box>
                  ),
                }}
              />
            </Box>

            <Box display="flex" alignItems="center" gap={1}>
              {/* Compact view toggle */}
              <FormControlLabel
                control={
                  <Switch
                    checked={compactView}
                    onChange={(e) => setCompactView(e.target.checked)}
                    color="primary"
                    size="small"
                  />
                }
                label={
                  <Typography variant="body2" color="text.secondary">
                    Compact View
                  </Typography>
                }
              />
            </Box>
          </Box>
        </Box>

        {/* Summary Card */}
        {showSummaryCard && (
          <Fade in={showSummaryCard}>
            <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
              <CardContent sx={{ py: 2 }}>
                <Grid container spacing={3} alignItems="center">
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <AccountBalanceIcon sx={{ color: 'white', fontSize: 28 }} />
                      <Box>
                        <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                          Grand Total
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                          ₱ {Number(grandTotal || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <InfoIcon sx={{ color: 'white', fontSize: 24 }} />
                      <Box>
                        <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                          Fiscal Year
                        </Typography>
                        <Typography variant="body1" sx={{ color: 'white', fontWeight: 'medium' }}>
                          {fiscalYear || 'Not Set'}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <TrendingUpIcon sx={{ color: 'white', fontSize: 24 }} />
                      <Box>
                        <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                          Budget Type
                        </Typography>
                        <Typography variant="body1" sx={{ color: 'white', fontWeight: 'medium' }}>
                          {budgetType || 'Not Set'}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box display="flex" alignItems="center" gap={1}>
                      {hasUnsavedChanges ? (
                        <WarningIcon sx={{ color: '#ffeb3b', fontSize: 24 }} />
                      ) : (
                        <CheckCircleIcon sx={{ color: '#4caf50', fontSize: 24 }} />
                      )}
                      <Box>
                        <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                          Status
                        </Typography>
                        <Typography variant="body1" sx={{ color: 'white', fontWeight: 'medium' }}>
                          {hasUnsavedChanges ? 'Unsaved Changes' : 'Up to Date'}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Fade>
        )}

        {/* Main content */}
        <Zoom in={true}>
          <Paper elevation={3} sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
          }}>
          {loadingCategories || !groupedItems ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress size={40} sx={{ color: '#375e38' }} />
            </Box>
          ) : (
            <TableContainer sx={{
              maxHeight: compactView ? '60vh' : '70vh',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '4px',
                '&:hover': {
                  background: '#a8a8a8',
                },
              },
            }}>
              <Table size={compactView ? "small" : "medium"} stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell
                      sx={{
                        bgcolor: '#375e38',
                        color: 'white',
                        fontWeight: 'bold',
                        width: '45%',
                        position: 'sticky',
                        top: 0,
                        zIndex: 2,
                        fontSize: compactView ? '0.875rem' : '1rem',
                        py: compactView ? 1 : 2
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={1}>
                        <AccountBalanceIcon fontSize="small" />
                        CATEGORIES
                      </Box>
                    </TableCell>
                    <TableCell
                      sx={{
                        bgcolor: '#375e38',
                        color: 'white',
                        fontWeight: 'bold',
                        width: '40%',
                        position: 'sticky',
                        top: 0,
                        zIndex: 2,
                        fontSize: compactView ? '0.875rem' : '1rem',
                        py: compactView ? 1 : 2
                      }}
                    >
                      <Box display="flex" alignItems="center" gap={1}>
                        <InfoIcon fontSize="small" />
                        SUB-CATEGORIES
                      </Box>
                    </TableCell>
                    <TableCell
                      sx={{
                        bgcolor: '#375e38',
                        color: 'white',
                        fontWeight: 'bold',
                        width: '15%',
                        position: 'sticky',
                        top: 0,
                        zIndex: 2,
                        fontSize: compactView ? '0.875rem' : '1rem',
                        py: compactView ? 1 : 2
                      }}
                      align="right"
                    >
                      <Box display="flex" alignItems="center" justifyContent="flex-end" gap={1}>
                        <TrendingUpIcon fontSize="small" />
                        AMOUNT
                      </Box>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* Render categories and subcategories */}
                  {Object.entries(groupedItems || {})
                    .filter(([categoryName]) => {
                      // Filter by search term if provided
                      if (searchTerm) {
                        return categoryName.toLowerCase().includes(searchTerm.toLowerCase());
                      }
                      return true;
                    })
                    .map(([categoryName, category]) => {
                    const isExpanded = expandedCategories[categoryName] !== false; // Default to expanded
                    const hasSubcategories = category.subcategories && category.subcategories.length > 0;

                    return (
                      <React.Fragment key={categoryName}>
                        {/* Category row with subtotal */}
                        <TableRow sx={{
                          bgcolor: category.isNetIsf ? '#e8f5e9' :
                                  category.isDiscount ? '#ffebee' :
                                  hasSubcategories ? '#f8f9fa' : 'white',
                          '&:hover': {
                            backgroundColor: category.isNetIsf ? '#e0f2f1' :
                                           category.isDiscount ? '#ffcdd2' :
                                           hasSubcategories ? '#e3f2fd' : 'rgba(0, 0, 0, 0.04)'
                          },
                          cursor: hasSubcategories ? 'pointer' : 'default',
                          transition: 'all 0.2s ease',
                          borderLeft: hasSubcategories ? '4px solid #1976d2' : 'none'
                        }}>
                          <TableCell
                            sx={{
                              fontWeight: 'bold',
                              color: category.isDiscount ? '#d32f2f' :
                                     category.isNetIsf ? '#2e7d32' :
                                     hasSubcategories ? '#1976d2' : 'inherit',
                              fontStyle: category.isDiscount ? 'italic' : 'normal',
                              fontSize: compactView ? '0.875rem' : '1rem',
                              py: compactView ? 1 : 1.5
                            }}
                            onClick={() => hasSubcategories && setExpandedCategories(prev => ({
                              ...prev,
                              [categoryName]: !isExpanded
                            }))}
                          >
                            <Box display="flex" alignItems="center" gap={1}>
                              {hasSubcategories && (
                                <Tooltip title={isExpanded ? "Collapse category" : "Expand category"}>
                                  <IconButton
                                    size="small"
                                    sx={{
                                      p: 0.5,
                                      color: '#1976d2',
                                      '&:hover': { backgroundColor: 'rgba(25, 118, 210, 0.08)' }
                                    }}
                                  >
                                    <ExpandMoreIcon
                                      sx={{
                                        transform: isExpanded ? 'rotate(0deg)' : 'rotate(-90deg)',
                                        transition: 'transform 0.3s ease'
                                      }}
                                    />
                                  </IconButton>
                                </Tooltip>
                              )}

                              {/* Category icon based on type */}
                              {category.isNetIsf ? (
                                <CheckCircleIcon sx={{ fontSize: 20, color: '#2e7d32' }} />
                              ) : category.isDiscount ? (
                                <WarningIcon sx={{ fontSize: 20, color: '#d32f2f' }} />
                              ) : hasSubcategories ? (
                                <AccountBalanceIcon sx={{ fontSize: 20, color: '#1976d2' }} />
                              ) : (
                                <InfoIcon sx={{ fontSize: 20, color: '#757575' }} />
                              )}

                              <Typography
                                variant={compactView ? "body2" : "body1"}
                                sx={{ fontWeight: 'bold' }}
                              >
                                {categoryName}
                              </Typography>

                              {categoryName === "Miscellaneous Income" && category.includesOthers && (
                                <Chip
                                  label={`+Others (${formatNumber(category.othersTotal || 0)})`}
                                  size="small"
                                  color="info"
                                  variant="outlined"
                                  sx={{ ml: 1, fontSize: '0.7rem' }}
                                />
                              )}

                              {hasSubcategories && (
                                <Chip
                                  label={`${category.subcategories?.length || 0} items`}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                  sx={{ ml: 'auto', fontSize: '0.7rem' }}
                                />
                              )}
                            </Box>
                          </TableCell>
                          <TableCell sx={{ fontSize: compactView ? '0.875rem' : '1rem' }}>
                            {hasSubcategories && (
                              <Typography variant="body2" color="text.secondary">
                                {isExpanded ? 'Click to collapse' : 'Click to expand'}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              fontWeight: 'bold',
                              fontSize: compactView ? '0.875rem' : '1rem',
                              color: category.subtotal > 0 ? '#2e7d32' : '#757575'
                            }}
                          >
                            {formatNumber(category.subtotal)}
                          </TableCell>
                        </TableRow>

                        {/* Subcategories - only show when expanded */}
                        {category.subcategories && category.subcategories.length > 0 && !category.isDiscount && !category.isNetIsf && isExpanded && (
                        <>
                          {category.subcategories.map((subcategory, index) => {
                            const key = `${category.categoryId}-${subcategory}`;

                            // Special handling for "Others" subcategory under Miscellaneous Income
                            let amount;
                            let isReadOnly = false;

                            if (subcategory === "Others" && categoryName === "Miscellaneous Income" && category.includesOthers) {
                              // Show the Others total from the original Others category
                              amount = formatNumber(category.othersTotal || 0);
                              isReadOnly = true;
                            } else {
                              // For regular subcategories, check if they came from Others category
                              const othersCategory = Object.values(groupedItems).find(cat => cat.categoryName === "Others");
                              if (othersCategory && othersCategory.subcategories && othersCategory.subcategories.includes(subcategory)) {
                                // This subcategory came from Others, use its amount from subcategoryAmounts
                                const othersKey = `${othersCategory.categoryId}-${subcategory}`;
                                amount = subcategoryAmounts[othersKey] || subcategoryAmounts[key] || '';
                              } else {
                                amount = subcategoryAmounts[key] || '';
                              }
                            }

                            return (
                              <TableRow key={`${categoryName}-${subcategory}-${index}`}>
                                <TableCell></TableCell>
                                <TableCell>
                                  {subcategory}
                                  {subcategory === "Others" && categoryName === "Miscellaneous Income" && (
                                    <Chip
                                      label="Auto-calculated"
                                      size="small"
                                      color="info"
                                      variant="outlined"
                                      sx={{ ml: 1, fontSize: '0.6rem' }}
                                    />
                                  )}
                                </TableCell>
                                <TableCell align="right">
                                  {isReadOnly ? (
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        textAlign: 'right',
                                        fontWeight: 'medium',
                                        color: '#1976d2',
                                        pr: 2
                                      }}
                                    >
                                      {amount}
                                    </Typography>
                                  ) : (
                                    <TextField
                                      size="small"
                                      value={amount}
                                      onChange={(e) => handleSubcategoryAmountChange(category.categoryId, subcategory, e.target.value)}
                                      InputProps={{
                                        inputComponent: NumberFormatCustom,
                                        inputProps: {
                                          style: { textAlign: 'right' }
                                        }
                                      }}
                                      sx={{
                                        width: '150px',
                                        '& .MuiInputBase-root': {
                                          display: 'flex',
                                          justifyContent: 'flex-end'
                                        }
                                      }}
                                    />
                                  )}
                                </TableCell>
                              </TableRow>
                            );
                          })}
                          
                          {/* New subcategory input field */}
                          {addingToCategoryId === category.categoryId && (
                            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                              <TableCell></TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  fullWidth
                                  placeholder="New subcategory name"
                                  value={newSubcategories[category.categoryId] || ''}
                                  onChange={(e) => handleNewSubcategoryChange(category.categoryId, e.target.value)}
                                />
                              </TableCell>
                              <TableCell align="right">
                                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                                  <IconButton 
                                    color="primary" 
                                    onClick={() => handleSaveNewSubcategory(category.categoryId, categoryName)}
                                    size="small"
                                  >
                                    <SaveIcon />
                                  </IconButton>
                                  <IconButton 
                                    color="secondary" 
                                    onClick={() => handleCancelAddSubcategory(category.categoryId)}
                                    size="small"
                                  >
                                    <CancelIcon />
                                  </IconButton>
                                </Box>
                              </TableCell>
                            </TableRow>
                          )}
                          
                          {/* Add Item button */}
                          {!category.isDiscount && !category.isNetIsf && !addingToCategoryId && (
                            <TableRow>
                              <TableCell></TableCell>
                              <TableCell colSpan={2}>
                                <Button
                                  startIcon={<AddIcon />}
                                  size="small"
                                  onClick={() => handleAddSubcategoryField(category.categoryId, categoryName)}
                                  sx={{ 
                                    mt: 1, 
                                    fontSize: '0.75rem',
                                    color: '#4caf50',
                                    '&:hover': {
                                      backgroundColor: 'rgba(76, 175, 80, 0.08)'
                                    }
                                  }}
                                >
                                  Add Item
                                </Button>
                              </TableCell>
                            </TableRow>
                          )}
                        </>
                      )}
                    </React.Fragment>
                  );
                  })}
                </TableBody>
                <TableFooter>
                  {/* Grand Total row */}
                  <TableRow>
                    <TableCell
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: '#375e38',
                        color: 'white',
                        position: 'sticky',
                        bottom: 0,
                        zIndex: 1
                      }}
                    >
                      Grand Total
                    </TableCell>
                    <TableCell
                      sx={{
                        bgcolor: '#375e38',
                        color: 'white',
                        position: 'sticky',
                        bottom: 0,
                        zIndex: 1
                      }}
                    ></TableCell>
                    <TableCell
                      align="right"
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: '#375e38',
                        color: 'white',
                        position: 'sticky',
                        bottom: 0,
                        zIndex: 1
                      }}
                    >
                      {formatNumber(grandTotal)}
                    </TableCell>
                  </TableRow>
                </TableFooter>
              </Table>
            </TableContainer>
          )}
        </Paper>
        </Zoom>

        {/* Export Menu */}
        <Menu
          anchorEl={exportMenuAnchor}
          open={Boolean(exportMenuAnchor)}
          onClose={() => setExportMenuAnchor(null)}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handleExportToExcel}>
            <GetAppIcon sx={{ mr: 1 }} />
            Export to CSV
          </MenuItem>
        </Menu>
        
        {/* Discount Settings Dialog */}
        <Dialog 
          open={discountSettingsOpen} 
          onClose={handleCloseDiscountSettings}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>ISF Discount Settings</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={applyDiscount} 
                    onChange={handleApplyDiscountChange}
                    color="primary"
                  />
                }
                label="Apply discount to ISF"
              />
            </Box>
            
            {applyDiscount && (
              <>
                <FormControl fullWidth sx={{ mt: 2 }}>
                  <InputLabel>Discount Type</InputLabel>
                  <Select
                    value={discountType}
                    onChange={handleDiscountTypeChange}
                    label="Discount Type"
                  >
                    <MenuItem value="percentage">Percentage (%)</MenuItem>
                    <MenuItem value="fixed">Fixed Amount (₱)</MenuItem>
                  </Select>
                </FormControl>
                
                {discountType === "percentage" ? (
                  <TextField
                    label="Discount Percentage"
                    type="number"
                    value={discountValue}
                    onChange={handleDiscountValueChange}
                    fullWidth
                    margin="normal"
                    InputProps={{
                      endAdornment: <Typography>%</Typography>,
                      inputProps: { 
                        min: 0,
                        max: 100
                      }
                    }}
                  />
                ) : (
                  <TextField
                    label="Discount Amount"
                    value={discountValue}
                    onChange={handleDiscountValueChange}
                    fullWidth
                    margin="normal"
                    InputProps={{
                      inputComponent: NumberFormatCustom,
                      startAdornment: <Typography sx={{ mr: 1 }}>₱</Typography>
                    }}
                  />
                )}
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDiscountSettings}>Cancel</Button>
            <Button 
              onClick={handleSaveDiscountSettings} 
              color="primary" 
              variant="contained"
              disabled={savingSettings}
            >
              {savingSettings ? <CircularProgress size={24} /> : "Save"}
            </Button>
          </DialogActions>
        </Dialog>
        
        {/* Other dialogs */}
        <Dialog
          open={deleteConfirmOpen}
          onClose={() => setDeleteConfirmOpen(false)}
        >
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete this item? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button 
              onClick={() => setDeleteConfirmOpen(false)} 
              color="primary"
              variant="outlined"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleConfirmDelete} 
              color="error"
              variant="contained"
              autoFocus
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Test button removed */}
      </Paper>
      <ToastContainer 
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </>
  );
};

// Add prop types
IncomeTable.propTypes = {
  data: PropTypes.array,
  onGrandTotalUpdate: PropTypes.func
};

export default IncomeTable;


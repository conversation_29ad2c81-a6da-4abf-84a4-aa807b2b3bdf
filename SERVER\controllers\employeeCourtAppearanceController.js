const EmployeeCourtAppearance = require("../models/employeeCourtAppearance");
const Settings = require("../models/Settings");
const PersonnelServices = require("../models/personnelServices");
const {
  numberFilter,
  dateFilter,
  textFilter,
} = require("../utils/controller_get_process");

// GET all court appearances
exports.getAllAppearances = async (req, res) => {
  try {
    const activeSettings = await Settings.findOne({ isActive: true });

    if (
      !activeSettings ||
      !activeSettings.fiscalYear ||
      !activeSettings.courtAppearance
    ) {
      return res.status(400).json({
        message: "Active settings with court appearance not found.",
      });
    }

    const fiscalYear = activeSettings.fiscalYear;
    const budgetType = activeSettings.budgetType;
    const rate = activeSettings.courtAppearance;

    // Kumuha ng permanent personnel para sa active fiscal year at may status na "Not Submitted", "Returned", o "Draft"
    // FILTER SPECIFICALLY FOR LEGAL SERVICES DEPARTMENT (or show all if no LEGAL SERVICES exists)
    const personnelQuery = {
      statusOfAppointment: { $regex: /^permanent$/i },
      fiscalYear: fiscalYear,
      status: { $in: ["Not Submitted", "Returned", "Draft"] } // Fixed: "Draft" not "Drafts"
    };

    // First try to find LEGAL SERVICES personnel
    const legalServicesQuery = {
      ...personnelQuery,
      $or: [
        { department: { $regex: /^LEGAL SERVICES$/i } },
        { Department: { $regex: /^LEGAL SERVICES$/i } }
      ]
    };

    console.log("Legal Services Query:", JSON.stringify(legalServicesQuery, null, 2));

    let personnelList = await PersonnelServices.find(legalServicesQuery);

    console.log(`Found ${personnelList.length} LEGAL SERVICES personnel for court appearances`);

    // If no LEGAL SERVICES personnel found, show all personnel for now (for testing)
    if (personnelList.length === 0) {
      console.log("No LEGAL SERVICES personnel found. Showing all personnel for testing...");
      personnelList = await PersonnelServices.find(personnelQuery);
      console.log(`Found ${personnelList.length} total personnel for court appearances`);
    }

    // If no LEGAL SERVICES personnel found, let's check what data exists
    if (personnelList.length === 0) {
      console.log("No LEGAL SERVICES personnel found. Checking available data...");

      // Check all personnel for this fiscal year
      const allPersonnelThisFiscalYear = await PersonnelServices.find({
        fiscalYear: fiscalYear
      }).select('department Department employeeFullName statusOfAppointment status').limit(10);

      console.log(`Total personnel for fiscal year ${fiscalYear}:`, allPersonnelThisFiscalYear.length);
      console.log("Sample records:", allPersonnelThisFiscalYear.slice(0, 3));

      // Check all permanent personnel regardless of status
      const allPermanentPersonnel = await PersonnelServices.find({
        statusOfAppointment: { $regex: /^permanent$/i },
        fiscalYear: fiscalYear
      }).select('department Department employeeFullName status').limit(10);

      console.log("Permanent personnel count:", allPermanentPersonnel.length);
      console.log("Permanent personnel sample:", allPermanentPersonnel.slice(0, 3));

      // Check what departments exist
      const departments = [...new Set(allPermanentPersonnel.map(p => p.department || p.Department).filter(Boolean))];
      console.log("Available departments:", departments);

      // Check what statuses exist
      const statuses = [...new Set(allPermanentPersonnel.map(p => p.status).filter(Boolean))];
      console.log("Available statuses:", statuses);
    }

    // Kumuha ng existing court appearances
    const appearanceRecords = await EmployeeCourtAppearance.find({ fiscalYear });

    // I-merge ang records at isama ang status mula sa personnel
    const merged = personnelList.map((emp) => {
      const record = appearanceRecords.find(
        (r) => r.employeeNumber === emp.employeeNumber
      );

      const noOfCourtAppearance = record?.noOfCourtAppearance || 0;
      const courtAppearanceAmount = rate * noOfCourtAppearance;

      return {
        _id: record?._id || `${emp._id}_temp`,
        employeeNumber: emp.employeeNumber,
        employeeFullName: emp.employeeFullName,
        positionTitle: emp.positionTitle,
        department: emp.department,
        division: emp.division,
        region: emp.region,
        noOfCourtAppearance,
        courtAppearanceAmount,
        fiscalYear,
        budgetType,
        processBy: record?.processBy || "",
        processDate: record?.processDate || "",
        status: emp.status, // Ipinapasa ang status (e.g., "Submitted", "Not Submitted")
      };
    });

    return res.json({
      data: merged,
      totalRecords: merged.length,
      currentPage: 1,
      totalPages: 1,
    });
  } catch (err) {
    console.error("getAllAppearances error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
};


// CREATE a new court appearance
exports.addAppearance = async (req, res) => {
  try {
    const {
      employeeNumber,
      employeeFullName,
      positionTitle,
      department,
      division,
      region,
      noOfCourtAppearance,
      processBy,
      processDate,
      budgetType,
    } = req.body;

    const activeSettings = await Settings.findOne({ isActive: true });
    if (!activeSettings || !activeSettings.courtAppearance) {
      return res
        .status(400)
        .json({ message: "Active settings with court appearance not found." });
    }

    // Gumamit ng budgetType mula sa request o settings
    const budgetTypeValue = budgetType || activeSettings.budgetType;
    if (!budgetTypeValue) {
      return res.status(400).json({ message: "Budget type is required." });
    }

    // Compute the court appearance amount using the active settings rate
    const courtAppearanceAmount =
      noOfCourtAppearance * activeSettings.courtAppearance;
    const fiscalYear = activeSettings.fiscalYear; // gamitin ang active fiscalYear

    const newEntry = new EmployeeCourtAppearance({
      employeeNumber,
      employeeFullName,
      positionTitle,
      department,
      division,
      region,
      noOfCourtAppearance,
      courtAppearanceAmount,
      processBy,
      processDate,
      fiscalYear,
      budgetType,
    });

    await newEntry.save();

    // Update Personnel record kung umiiral (para lamang sa napiling personnel)
    const personnelRecord = await PersonnelServices.findOne({
      employeeNumber: employeeNumber,
      fiscalYear: fiscalYear,
    });
    if (personnelRecord) {
      personnelRecord.courtAppearance = courtAppearanceAmount;
      await personnelRecord.save();
    }

    res.status(201).json({ message: "Record created successfully.", data: newEntry });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Failed to create record." });
  }
};

// UPDATE a court appearance
exports.editAppearance = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const activeSettings = await Settings.findOne({ isActive: true });
    if (updates.noOfCourtAppearance && activeSettings?.courtAppearance) {
      updates.courtAppearanceAmount = updates.noOfCourtAppearance * activeSettings.courtAppearance;
    }
    // Siguraduhin na ginagamit ang active fiscalYear
    if (activeSettings && activeSettings.fiscalYear) {
      updates.fiscalYear = activeSettings.fiscalYear;
    }

    if (updates.budgetType) {
      const budgetTypeValue = updates.budgetType || activeSettings.budgetType;
      if (!budgetTypeValue) {
        return res.status(400).json({ message: "Budget type is required." });
      }
    }

    const updated = await EmployeeCourtAppearance.findByIdAndUpdate(id, updates, {
      new: true,
      runValidators: true,
    });

    if (!updated) return res.status(404).json({ error: "Record not found." });

    // Update the corresponding Personnel record kung umiiral
    const personnelRecord = await PersonnelServices.findOne({
      employeeNumber: updated.employeeNumber,
      fiscalYear: updated.fiscalYear,
    });
    if (personnelRecord) {
      personnelRecord.courtAppearance = updated.courtAppearanceAmount;
      await personnelRecord.save();
    }

    res.json({ message: "Record updated successfully.", data: updated });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Failed to update record." });
  }
};

// DELETE a court appearance
exports.deleteAppearance = async (req, res) => {
  try {
    const { id } = req.params;

    const deleted = await EmployeeCourtAppearance.findByIdAndDelete(id);
    if (!deleted) return res.status(404).json({ error: "Record not found." });

    res.json({ message: "Record deleted successfully." });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Failed to delete record." });
  }
};

exports.getSumOfCourtAppearanceAmount = async (req, res) => {
  try {
    // Gamitin ang active fiscalYear sa pag‑sum
    const activeSettings = await Settings.findOne({ isActive: true });
    if (!activeSettings) {
      return res.status(400).json({ message: "Active settings not found." });
    }
    const fiscalYear = activeSettings.fiscalYear;

    const result = await EmployeeCourtAppearance.aggregate([
      { $match: { fiscalYear } },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: "$courtAppearanceAmount" },
        },
      },
    ]);

    const totalAmount = result.length > 0 ? result[0].totalAmount : 0;

    res.json({ totalAmount });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Failed to calculate total amount." });
  }
};

const express = require("express");
const router = express.Router();
const {
  getAllTitleMappings,
  getCapitalOutlayTitleMappings,
  getTitleMappingById,
  createTitleMapping,
  updateTitleMapping,
  deleteTitleMapping,
  getAvailableChartOfAccounts,
  syncFromChartOfAccounts
} = require("../controllers/titleMappingController");

// Get all title mappings
router.get("/title-mappings", getAllTitleMappings);

// Get capital outlay title mappings only
router.get("/title-mappings/capital-outlay", getCapitalOutlayTitleMappings);

// Get available chart of accounts for mapping
router.get("/title-mappings/available-accounts", getAvailableChartOfAccounts);

// Sync title mappings from chart of accounts
router.post("/title-mappings/sync", syncFromChartOfAccounts);

// Get a single title mapping by ID
router.get("/title-mappings/:id", getTitleMappingById);

// Create a new title mapping
router.post("/title-mappings", createTitleMapping);

// Update a title mapping
router.put("/title-mappings/:id", updateTitleMapping);

// Delete a title mapping
router.delete("/title-mappings/:id", deleteTitleMapping);

module.exports = router;

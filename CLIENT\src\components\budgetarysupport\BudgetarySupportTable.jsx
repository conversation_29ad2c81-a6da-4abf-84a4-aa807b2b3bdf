import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  TextField,
  IconButton,
  CircularProgress,
  Alert,
  InputAdornment,
  Button,
  Chip,
  Tooltip,
  Card,
  CardContent,
  Grid,
  Divider,
  FormControlLabel,
  Switch,
  Menu,
  MenuItem,
  Fade,
  Zoom
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import RefreshIcon from '@mui/icons-material/Refresh';
import GetAppIcon from '@mui/icons-material/GetApp';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import AddIcon from '@mui/icons-material/Add';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import api from '../../config/api';
import { toast } from 'react-toastify';
import { NumericFormat } from 'react-number-format';
import { useUser } from '../../context/UserContext';

const BudgetarySupportTable = ({ fiscalYear, budgetType }) => {
  const [amount, setAmount] = useState(0);
  const [exportMenuAnchor, setExportMenuAnchor] = useState(null);
  const [compactView, setCompactView] = useState(false);
  const [showSummaryCard, setShowSummaryCard] = useState(true);
  const queryClient = useQueryClient();
  const { currentUser } = useUser();

  // Custom component for numeric input formatting
  const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
    const { onChange, ...other } = props;
    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        thousandSeparator=","
        decimalScale={2}
        fixedDecimalScale={false} // Don't force decimal places
        allowNegative={false} // Prevent negative values
        allowLeadingZeros={false}
        valueIsNumericString={true}
        onValueChange={(values, sourceInfo) => {
          // Only trigger onChange if the value actually changed
          // and avoid triggering on format-only changes
          if (sourceInfo.source !== 'prop' && onChange) {
            const numericValue = values.value || "";
            onChange({
              target: {
                name: props.name,
                value: numericValue,
              },
            });
          }
        }}
      />
    );
  });

  // Check if we have valid fiscalYear and budgetType
  const hasValidParams = !!fiscalYear && !!budgetType;

  // Fetch MOOE data to calculate subsidy total
  const { data: mooeData } = useQuery({
    queryKey: ['mooe', fiscalYear, budgetType],
    queryFn: async () => {
      if (!hasValidParams) return [];
      const response = await api.get('/mooe', {
        params: { fiscalYear, budgetType }
      });
      return response.data || [];
    },
    enabled: hasValidParams
  });

  // Fetch Capital Outlay data to calculate subsidy total
  const { data: capitalOutlayData } = useQuery({
    queryKey: ['capital-outlay', fiscalYear, budgetType],
    queryFn: async () => {
      if (!hasValidParams) return [];
      const response = await api.get('/capital-outlay', {
        params: { fiscalYear, budgetType }
      });
      return response.data || [];
    },
    enabled: hasValidParams
  });

  // Use React Query for existing budgetary support data (for reference only)
  const {
    data,
    isLoading,
    error
  } = useQuery({
    queryKey: ['budgetary-support', fiscalYear, budgetType],
    queryFn: async () => {
      if (!hasValidParams) {
        console.log('Missing required parameters:', { fiscalYear, budgetType });
        return null;
      }

      console.log('Fetching budgetary support with params:', { fiscalYear, budgetType });
      const response = await api.get('/budgetary-support', {
        params: { fiscalYear, budgetType }
      });
      console.log('Budgetary support response:', response.data);
      return response.data && response.data.length > 0 ? response.data[0] : null;
    },
    enabled: hasValidParams, // Only run query if we have valid params
    onError: (err) => {
      console.error('Error fetching budgetary support data:', err);
      toast.error(`Error loading budgetary support data: ${err.message}`);
    }
  });

  // Auto-calculate amount based on MOOE Subsidy + Capital Outlay Subsidy
  const calculatedAmount = useMemo(() => {
    // Calculate MOOE subsidy total
    const mooeSubsidyTotal = Array.isArray(mooeData)
      ? mooeData.reduce((sum, item) => sum + (Number(item.subsidy) || 0), 0)
      : 0;

    // Calculate Capital Outlay subsidy total
    const capitalOutlaySubsidyTotal = Array.isArray(capitalOutlayData)
      ? capitalOutlayData.reduce((sum, item) => sum + (Number(item.subsidy) || 0), 0)
      : 0;

    const total = mooeSubsidyTotal + capitalOutlaySubsidyTotal;
    console.log('BudgetarySupportTable: Auto-calculated amount:', {
      mooeSubsidyTotal,
      capitalOutlaySubsidyTotal,
      total
    });

    return total;
  }, [mooeData, capitalOutlayData]);

  // Set amount to calculated value
  useEffect(() => {
    setAmount(calculatedAmount);
  }, [calculatedAmount]);

  // Amount is now auto-calculated, no manual changes allowed

  // Enhanced export functionality
  const handleExportToExcel = useCallback(() => {
    const exportData = [{
      'Description': 'Subsidy to the National Irrigation Administration - Operating Requirements',
      'Amount': parseFloat(amount || 0),
      'Fiscal Year': fiscalYear,
      'Budget Type': budgetType,
      'Region': currentUser?.Region || '',
      'Last Updated': lastSaved ? new Date(lastSaved).toLocaleString() : 'Never',
      'Status': data?.status || 'Not Submitted'
    }];

    // Create CSV content
    const headers = Object.keys(exportData[0]);
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Budgetary_Support_${fiscalYear}_${budgetType}_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    toast.success("Budgetary support data exported successfully!");
    setExportMenuAnchor(null);
  }, [amount, fiscalYear, budgetType, currentUser?.Region, lastSaved, data?.status]);

  // Refresh data
  const handleRefresh = useCallback(() => {
    queryClient.invalidateQueries(['budgetary-support', fiscalYear, budgetType]);
    toast.info("Data refreshed");
  }, [queryClient, fiscalYear, budgetType]);

  // No add/edit functionality - amount is auto-calculated

  if (!hasValidParams) {
    return (
      <Box sx={{ width: '100%', mb: 4 }}>
        <Alert severity="warning">
          Cannot load budgetary support data: Missing fiscal year or budget type.
        </Alert>
      </Box>
    );
  }

  if (isLoading) {
    return (
      <Box sx={{ width: '100%', mb: 4, display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ width: '100%', mb: 4 }}>
        <Alert severity="error">
          Error loading budgetary support data: {error.message}
        </Alert>
      </Box>
    );
  }

  // Enhanced toolbar component
  const renderEnhancedToolbar = () => (
    <Box sx={{ mb: 3 }}>
      {/* Top Row - Main Controls */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Box display="flex" alignItems="center" gap={2}>
          {/* Info chip showing auto-calculation */}
          <Chip
            icon={<CheckCircleIcon />}
            label="Auto-calculated from MOOE + Capital Outlay Subsidies"
            color="info"
            variant="outlined"
            size="medium"
          />
        </Box>

        <Box display="flex" alignItems="center" gap={1}>
          {/* Export button */}
          <Tooltip title="Export data">
            <IconButton
              onClick={(e) => setExportMenuAnchor(e.currentTarget)}
              color="primary"
            >
              <GetAppIcon />
            </IconButton>
          </Tooltip>

          {/* Refresh button */}
          <Tooltip title="Refresh data">
            <IconButton onClick={handleRefresh} color="primary">
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          {/* View toggle */}
          <Tooltip title="Toggle compact view">
            <IconButton
              onClick={() => setCompactView(!compactView)}
              color={compactView ? "primary" : "default"}
            >
              <VisibilityIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Summary Card */}
      {showSummaryCard && (
        <Fade in={showSummaryCard}>
          <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
            <CardContent sx={{ py: 2 }}>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={3}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <AccountBalanceIcon sx={{ color: 'white', fontSize: 28 }} />
                    <Box>
                      <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                        Budgetary Support
                      </Typography>
                      <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                        ₱ {Number(amount || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <InfoIcon sx={{ color: 'white', fontSize: 24 }} />
                    <Box>
                      <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                        Fiscal Year
                      </Typography>
                      <Typography variant="body1" sx={{ color: 'white', fontWeight: 'medium' }}>
                        {fiscalYear || 'Not Set'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <TrendingUpIcon sx={{ color: 'white', fontSize: 24 }} />
                    <Box>
                      <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                        Budget Type
                      </Typography>
                      <Typography variant="body1" sx={{ color: 'white', fontWeight: 'medium' }}>
                        {budgetType || 'Not Set'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <CheckCircleIcon sx={{ color: '#4caf50', fontSize: 24 }} />
                    <Box>
                      <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                        Status
                      </Typography>
                      <Typography variant="body1" sx={{ color: 'white', fontWeight: 'medium' }}>
                        Auto-calculated
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Fade>
      )}
    </Box>
  );

  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      {/* Enhanced Toolbar */}
      {renderEnhancedToolbar()}

      <Zoom in={true}>
        <TableContainer component={Paper} sx={{
          boxShadow: 3,
          borderRadius: 2,
          overflow: 'hidden'
        }}>
          <Table size={compactView ? "small" : "medium"}>
            <TableHead>
              <TableRow sx={{
                background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                '& .MuiTableCell-head': {
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: compactView ? '0.875rem' : '1rem'
                }
              }}>
                <TableCell colSpan={2}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <AccountBalanceIcon />
                    BUDGETARY SUPPORT
                  </Box>
                </TableCell>
                <TableCell align="right" sx={{ width: '200px' }}>
                  <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                    Auto-calculated
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow sx={{
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                transition: 'background-color 0.2s ease'
              }}>
                <TableCell colSpan={2} sx={{ pl: 2 }}>
                  <Typography variant="subtitle1" sx={{
                    fontWeight: 'medium',
                    fontSize: compactView ? '0.875rem' : '1rem'
                  }}>
                    Subsidy to the National Irrigation Administration:
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2" color="text.secondary">-</Typography>
                </TableCell>
              </TableRow>

              <TableRow sx={{
                '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                transition: 'background-color 0.2s ease'
              }}>
                <TableCell sx={{ width: '30px', fontSize: compactView ? '0.875rem' : '1rem' }}>
                  a.
                </TableCell>
                <TableCell sx={{ fontSize: compactView ? '0.875rem' : '1rem' }}>
                  Operating Requirements
                  <Chip
                    label="Auto-calculated"
                    size="small"
                    color="info"
                    sx={{ ml: 1, fontSize: '0.75rem' }}
                  />
                </TableCell>
                <TableCell align="right">
                  <Typography sx={{
                    fontWeight: 'medium',
                    fontSize: compactView ? '14px' : '16px',
                    color: amount > 0 ? 'success.main' : 'text.secondary'
                  }}>
                    ₱ {Number(amount || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow sx={{
                background: 'linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%)',
                '& .MuiTableCell-root': {
                  fontWeight: 'bold',
                  fontSize: compactView ? '0.875rem' : '1rem'
                }
              }}>
                <TableCell colSpan={2}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <TrendingUpIcon color="primary" fontSize="small" />
                    TOTAL BUDGETARY SUPPORT
                  </Box>
                </TableCell>
                <TableCell align="right">
                  <Typography sx={{
                    fontWeight: 'bold',
                    fontSize: compactView ? '14px' : '16px',
                    color: amount > 0 ? 'success.main' : 'text.secondary'
                  }}>
                    ₱ {Number(amount || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </Typography>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Zoom>

      {/* Export Menu */}
      <Menu
        anchorEl={exportMenuAnchor}
        open={Boolean(exportMenuAnchor)}
        onClose={() => setExportMenuAnchor(null)}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleExportToExcel}>
          <GetAppIcon sx={{ mr: 1 }} />
          Export to CSV
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default BudgetarySupportTable;






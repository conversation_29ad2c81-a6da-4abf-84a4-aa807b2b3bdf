const Employee = require("../models/EmployeeList");
const PersonnelServices = require("../models/PersonnelServices");
const {
  numberFilter,
  dateFilter,
  textFilter,
  booleanFilter,
} = require("../utils/controller_get_process");

exports.getAllEmployees = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      orderBy,
      order = "asc",
      operator,
      Department,
      positionTitle,
      Division,
      Section,
      Region,
      StatusOfAppointment,
      sg,
      step,
      jg,
      Rate,
      charging,
      employeeID,
      EmployeeFullName,
      DateOfAppointment,
      DateOfBirth,
      Status,
    } = req.query;

    let query = {};

    if (search) {
      query.$or = [
        { Department: { $regex: search, $options: "i" } },
        { positionTitle: { $regex: search, $options: "i" } },
        { Division: { $regex: search, $options: "i" } },
        { Section: { $regex: search, $options: "i" } },
        { Region: { $regex: search, $options: "i" } },
        { StatusOfAppointment: { $regex: search, $options: "i" } },
        { EmployeeFullName: { $regex: search, $options: "i" } },
        { employeeID: { $regex: search, $options: "i" } },
        { positionTitle: { $regex: search, $options: "i" } },
        { department: { $regex: search, $options: "i" } },
        { Status: { $regex: search, $options: "i" } },
      ];
    }

    textFilter(query, {
      positionTitle,
      StatusOfAppointment,
      jg,
      charging,
      employeeID,
      EmployeeFullName,
      Department,
      Division,
      Section,
      Region,
      Status,
    });
    numberFilter(query, { sg, step, Rate }, operator);
    dateFilter(query, { DateOfAppointment, DateOfBirth });

    const sortByField = orderBy || "createdAt";
    const sortOrder = order.toLowerCase() === "desc" ? -1 : 1;
    const sortQuery = { [sortByField]: sortOrder };

    const pageNum = Math.max(1, Number(page));
    const limitNum = Math.max(1, Number(limit));
    const skip = (pageNum - 1) * limitNum;

    const employees = await Employee.find(query)
      .skip(skip)
      .limit(limitNum)
      .sort(sortQuery);
    const totalRecords = await Employee.countDocuments(query);

    return res.json({
      employees,
      totalPages: Math.ceil(totalRecords / limitNum),
      currentPage: pageNum,
      totalRecords,
    });
  } catch (e) {
    console.error(e);
    return res.status(500).json({ error: "Something went wrong." });
  }
};

exports.addEmployee = async (req, res) => {
  try {
    const newEmployee = new Employee(req.body);
    await newEmployee.save();
    return res.status(201).json({
      message: "Employee created successfully.",
      employee: newEmployee,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to create employee." });
  }
};

exports.editEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const updatedEmployee = await Employee.findByIdAndUpdate(id, req.body, {
      new: true,
      runValidators: true,
    });

    if (!updatedEmployee) {
      return res.status(404).json({ error: "Employee not found." });
    }

    return res.json({
      message: "Employee updated successfully.",
      employee: updatedEmployee,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to update employee." });
  }
};

exports.deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const deletedEmployee = await Employee.findByIdAndDelete(id);
    if (!deletedEmployee) {
      return res.status(404).json({ error: "Employee not found." });
    }

    return res.json({ message: "Employee deleted successfully." });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to delete employee." });
  }
};

exports.getPersonnels = async (req, res) => {
  try {
    const { search } = req.query;

    let query = {};

    if (search) {
      query.$or = [
        { EmployeeFullName: { $regex: search, $options: "i" } },
        { employeeFullName: { $regex: search, $options: "i" } },
        { employeeID: { $regex: search, $options: "i" } },
        { employeeNumber: { $regex: search, $options: "i" } },
        { Department: { $regex: search, $options: "i" } },
        { department: { $regex: search, $options: "i" } },
        { positionTitle: { $regex: search, $options: "i" } },
        { PositionTitle: { $regex: search, $options: "i" } },
        { Status: { $regex: search, $options: "i" } },
      ];
    }

    const employees = await PersonnelServices.find(query).limit(20);

    const formattedEmployees = employees.map((emp) => ({
      _id: emp._id,
      employeeNumber: emp.employeeNumber,
      employeeFullName: emp.EmployeeFullName || emp.employeeFullName || "",
      positionTitle: emp.PositionTitle || emp.positionTitle || "",
      department: emp.Department || emp.department || "",
      status: emp.Status || "Active",
    }));

    return res.json(formattedEmployees);
  } catch (error) {
    console.error("Error in getPersonnels:", error);
    return res.status(500).json({ error: "Failed to fetch personnel data" });
  }
};

// Get employees eligible for loyalty pay based on years of service
exports.getEligibleForLoyaltyPay = async (req, res) => {
  try {
    console.log("Getting eligible employees for loyalty pay");

    // Get active settings to determine the fiscal year
    const Settings = require("../models/Settings");
    const {
      calculateLoyaltyPayYearsOfService,
      getEligibleLoyaltyPayYear
    } = require("../utils/loyaltyPayUtils");

    const activeSettings = await Settings.findOne({ isActive: true }).lean();

    // Use fiscal year from settings, or fallback to current year
    const fiscalYear = activeSettings?.fiscalYear
      ? parseInt(activeSettings.fiscalYear)
      : new Date().getFullYear();
    
    console.log(`Using fiscal year: ${fiscalYear}`);

    // Get cutoff date from settings
    const cutoffDate = activeSettings?.loyaltyPay?.cutoffDate || "06-22";
    console.log(`Using loyalty pay cutoff date: ${cutoffDate}`);

    // First try to get data from PersonnelServices model
    const personnelServices = await PersonnelServices.find({
      employeeStatus: "Active",
      DateOfAppointment: { $exists: true, $ne: null }
    }).lean();

    console.log(`Found ${personnelServices.length} active personnel services records with DateOfAppointment`);

    // Filter employees based on years of service with June 22 cutoff
    const eligibleEmployees = [];

    for (const employee of personnelServices) {
      if (!employee.DateOfAppointment) continue;

      // Calculate years of service with June 22 cutoff
      const yearsOfService = calculateLoyaltyPayYearsOfService(
        employee.DateOfAppointment,
        fiscalYear.toString(),
        cutoffDate
      );

      // Get the eligible loyalty pay year (milestone)
      const eligibleYear = getEligibleLoyaltyPayYear(yearsOfService);

      console.log(`Employee: ${employee.employeeFullName}, Years of Service: ${yearsOfService}, Eligible Year: ${eligibleYear}`);

      // Only include employees who have reached a milestone year
      if (eligibleYear !== null) {
        eligibleEmployees.push({
          ...employee,
          yearsInService: eligibleYear,
          actualYearsOfService: yearsOfService // Include actual years for reference
        });
      }
    }
    
    console.log(`Found ${eligibleEmployees.length} eligible employees`);
    
    // Format the response
    const formattedEmployees = eligibleEmployees.map(emp => ({
      _id: emp._id,
      employeeNumber: emp.employeeNumber,
      employeeFullName: emp.employeeFullName,
      positionTitle: emp.positionTitle,
      department: emp.department,
      division: emp.division,
      region: emp.region,
      dateOfAppointment: emp.DateOfAppointment,
      yearsInService: emp.yearsInService,
      actualYearsOfService: emp.actualYearsOfService,
      cutoffDate: cutoffDate // Include cutoff date for reference
    }));
    
    return res.status(200).json(formattedEmployees);
  } catch (err) {
    console.error("Error getting eligible employees for loyalty pay:", err);
    return res.status(500).json({ message: "Server error", error: err.message });
  }
};

// Get employees eligible for retirement
exports.getEligibleForRetirement = async (req, res) => {
  try {
    console.log("Getting eligible employees for retirement");
    
    // Get active settings to determine the fiscal year
    const Settings = require("../models/Settings");
    const activeSettings = await Settings.findOne({ isActive: true }).lean();
    
    // Use fiscal year from settings, or fallback to current year
    const fiscalYear = activeSettings?.fiscalYear 
      ? parseInt(activeSettings.fiscalYear) 
      : new Date().getFullYear();
    
    console.log(`Using fiscal year: ${fiscalYear}`);
    const currentDate = new Date();
    
    // First try to get data from PersonnelServices model
    const personnelServices = await PersonnelServices.find({
      employeeStatus: "Active",
      DateOfAppointment: { $exists: true, $ne: null },
      DateOfBirth: { $exists: true, $ne: null }
    }).lean();

    console.log(`Found ${personnelServices.length} active personnel services records with DateOfAppointment and DateOfBirth`);
    
    // Filter employees based on age and years of service
    const eligibleEmployees = [];
    
    for (const employee of personnelServices) {
      if (!employee.DateOfAppointment || !employee.DateOfBirth) continue;

      // Calculate age more accurately
      const birthDate = new Date(employee.DateOfBirth);
      let age = currentDate.getFullYear() - birthDate.getFullYear();
      const monthDiff = currentDate.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
        age--;
      }

      // Calculate years of service more accurately
      const appointmentDate = new Date(employee.DateOfAppointment);
      let yearsOfService = currentDate.getFullYear() - appointmentDate.getFullYear();
      const serviceMonthDiff = currentDate.getMonth() - appointmentDate.getMonth();
      if (serviceMonthDiff < 0 || (serviceMonthDiff === 0 && currentDate.getDate() < appointmentDate.getDate())) {
        yearsOfService--;
      }

      // Check if employee is eligible for compulsory retirement (65 years old)
      const isEligibleForCompulsory = age >= 65;

      // Check if employee is eligible for optional retirement (60-64 years old with at least 15 years of service)
      const isEligibleForOptional = (age >= 60 && age < 65) && yearsOfService >= 15;

      console.log(`Employee: ${employee.employeeFullName}, Age: ${age}, Years of Service: ${yearsOfService}, Birth: ${birthDate.toDateString()}, Appointment: ${appointmentDate.toDateString()}`);

      // Include employees who are eligible for either type of retirement
      if (isEligibleForCompulsory || isEligibleForOptional) {
        eligibleEmployees.push({
          ...employee,
          age,
          yearsOfService,
          retirementType: isEligibleForCompulsory ? "Compulsory" : "Optional",
          earnedLeaves: employee.earnedLeaves || 0 // Include earned leaves for terminal leave calculation
        });
      }
    }
    
    console.log(`Found ${eligibleEmployees.length} eligible employees for retirement from PersonnelServices`);

    // We now use ONLY PersonnelServices (no EmployeeList fallback)
    
    // Format the response with proper employee number format
    const formattedEmployees = eligibleEmployees.map(emp => {
      // Get the correct employee number
      let employeeNum = "";
      
      // Try to get the employee number from various possible fields
      if (emp.employeeNumber) {
        employeeNum = emp.employeeNumber;
      } else if (emp.EmployeeID) {
        employeeNum = emp.EmployeeID;
      } else if (emp.employeeID) {
        employeeNum = emp.employeeID;
      }
      
      console.log(`Employee ${emp.employeeFullName || emp.EmployeeFullName}: employee number = ${employeeNum}`);
      
      return {
        _id: emp._id,
        employeeNumber: employeeNum, // This should be a string like "336155"
        employeeFullName: emp.employeeFullName || emp.EmployeeFullName || "",
        positionTitle: emp.positionTitle || emp.PositionTitle || "",
        department: emp.department || emp.Department || "",
        division: emp.division || emp.Division || "",
        region: emp.region || emp.Region || "",
        age: emp.age,
        yearsOfService: emp.yearsOfService,
        retirementType: emp.retirementType,
        earnedLeaves: emp.earnedLeaves || 0 // Include earned leaves for terminal leave calculation
      };
    });

    // Log the formatted employees to verify the employee number format
    console.log("Employees with formatted numbers:", formattedEmployees.map(e => ({
      name: e.employeeFullName,
      employeeNumber: e.employeeNumber // Should be like "336155"
    })));

    return res.status(200).json(formattedEmployees);
  } catch (err) {
    console.error("Error getting eligible employees for retirement:", err);
    return res.status(500).json({ message: "Server error", error: err.message });
  }
};

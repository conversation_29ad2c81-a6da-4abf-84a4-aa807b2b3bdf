const {
  getAllAppearances,
  addAppearance,
  editAppearance,
  deleteAppearance,
  getSumOfCourtAppearanceAmount,
} = require("../controllers/employeeCourtAppearanceController");

const Router = require("express").Router;

// Import security middleware
const {
  authenticatedRoute,
  adminRoute,
  dueDateProtectedRoute,
  PERMISSION_LEVELS,
  ACCESS_SCOPE
} = require('../middleware/securityMiddleware');

const appearanceRouter = Router();

// 🔒 SECURED ROUTES

appearanceRouter.get("/court-appearances", ...authenticatedRoute(), getAllAppearances);
appearanceRouter.post("/court-appearances", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN, ACCESS_SCOPE.FULL), addAppearance);
appearanceRouter.put("/court-appearances/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN, ACCESS_SCOPE.FULL), editAppearance);
appearanceRouter.delete("/court-appearances/:id", ...dueDateProtectedRoute(PERMISSION_LEVELS.ADMIN, ACCESS_SCOPE.FULL), deleteAppearance);
appearanceRouter.get("/court-appearances/sum", ...authenticatedRoute(), getSumOfCourtAppearanceAmount);

module.exports = appearanceRouter;
